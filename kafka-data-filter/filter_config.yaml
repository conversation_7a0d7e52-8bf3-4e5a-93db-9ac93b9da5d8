# Kafka数据过滤程序配置文件
# 
# 此配置文件定义了Kafka连接参数、过滤规则和输出设置
# 请根据实际环境修改相应配置

# Kafka连接配置
kafka:
  # Kafka集群地址，多个地址用逗号分隔
  bootstrap_servers: "123.235.23.238:19301"

  # 要消费的topic
  topic: "moyeT_dfycb_website"

  # 消费者组ID
  group_id: "moye-check-data"

  # 消费起始位置：earliest（从最早消息开始）或 latest（从最新消息开始）
  auto_offset_reset: "latest"
  
  # 是否自动提交offset
  enable_auto_commit: true

  # 是否强制从最新消息开始消费（忽略已提交的offset）
  # 注意：启用此选项会导致跳过所有历史消息
  force_latest: false
  
  # 安全配置（可选）
  # security_protocol: "SASL_PLAINTEXT"
  # sasl_mechanism: "PLAIN"
  # sasl_username: "your_username"
  # sasl_password: "your_password"

# 过滤规则配置
filter:
  # 过滤规则列表，支持多个规则（OR逻辑：任一规则通过即可）
  #
  # 数组字段过滤语法说明：
  # - field[ANY].subfield - 数组中任意元素的子字段满足条件（默认行为）
  # - field[ALL].subfield - 数组中所有元素的子字段都满足条件
  # - field.subfield - 向后兼容语法，等同于 field[ANY].subfield
  #
  # 示例：
  # - n_content_spam[ANY].class2 NOT IN (...) - 数组中任意元素的class2不在排除列表中
  # - n_content_spam[ALL].class2 NOT IN (...) - 数组中所有元素的class2都不在排除列表中
  rules:
    # 规则1：重要媒体的首次采集内容（关注类别）
    # 使用显式ANY语法，确保数组中任意元素满足条件即可通过过滤
    - "y_is_first_gather = 1 AND n_content_spam[ANY].class2 NOT IN (\"广告信息\", \"股票资讯\", \"新闻合集\", \"历史科普\", \"明星娱乐\") AND y_yq_focus.label_name = \"关注\" AND n_media.nature IN (\"境外媒体\", \"党媒\",\"政府部门\")"

    # 规则2：不确定关注度但来源可靠的内容
    # 使用显式ANY语法，提高代码可读性
    - "y_is_first_gather = 1 AND n_content_spam[ANY].class2 NOT IN (\"广告信息\", \"股票资讯\", \"新闻合集\", \"历史科普\", \"明星娱乐\") AND y_yq_focus.label_name = \"不关注\" AND y_yq_focus.probability < 0.8 AND n_media.nature IN (\"境外媒体\", \"党媒\",\"政府部门\")"
    
    # 示例规则3：字符串包含
    # - "title CONTAINS \"重要\" AND content CONTAINS \"通知\""
    
    # 示例规则4：复杂逻辑组合
    # - "(priority = \"高\" OR urgency = \"紧急\") AND (department IN (\"技术部\", \"产品部\") OR level >= 5)"

# 输出配置
output:
  # 输出类型：console（控制台）、file（文件）、both（同时输出到控制台和文件）
  type: "both"

  # 输出格式：json（紧凑JSON）、pretty_json（格式化JSON）、text（文本格式）、json_array（JSON数组格式）
  format: "json_array"

  # 输出目录路径（新增）
  output_directory: "./output"

  # 文件输出路径（当type为file或both时必需）
  # 注意：如果同时指定了output_directory和file_path，将使用output_directory + 自动生成的文件名
  file_path: "filtered_messages3.json"

  # 文件命名策略配置（新增）
  file_naming:
    # 是否使用时间戳命名：true（自动生成带时间戳的文件名）、false（使用file_path指定的文件名）
    use_timestamp: true

    # 文件名前缀（当use_timestamp为true时使用）
    prefix: "filtered_messages"

    # 文件扩展名
    extension: ".json"

    # 时间戳格式（YYYYMMDD_HHMMSS）
    timestamp_format: "%Y%m%d_%H%M%S"

  # JSON数组输出配置（新增）
  json_array:
    # 是否启用增量追加模式
    incremental_append: true

    # 每次写入的消息数量（批量写入优化）
    batch_size: 10

    # 是否在程序启动时创建新文件（true：每次启动创建新文件，false：追加到现有文件）
    new_file_on_startup: true

  # 是否显示过滤详情
  show_filter_details: false

# 统计配置
statistics:
  # 是否启用统计功能
  enabled: true
  
  # 统计报告输出间隔（秒）
  interval: 10
  
  # 是否显示详细统计信息
  show_details: true

# 日志配置
logging:
  # 日志级别：DEBUG、INFO、WARNING、ERROR、CRITICAL
  level: "INFO"

# 自动清理配置
auto_cleanup:
  # 是否启用程序退出时自动删除消费者组
  enabled: true

  # 清理操作超时时间（秒）
  timeout: 30

  # 清理前是否备份消费者组信息
  backup_before_cleanup: true

  # 是否在异常退出时也执行清理（建议启用）
  cleanup_on_signal: true
