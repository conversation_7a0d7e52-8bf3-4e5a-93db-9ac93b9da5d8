"""
过滤规则解析器模块

负责解析复杂的过滤表达式，支持：
- 逻辑运算符：AND, OR, NOT
- 比较运算符：=, >, <, >=, <=, IN, NOT IN, CONTAINS
- 括号分组
- 多层嵌套字段访问
"""

import re
from typing import List, Dict, Any, Union, Optional, Tuple
from enum import Enum
import logging
from .data_accessor import DataAccessor, ArrayAccessResult


class TokenType(Enum):
    """Token类型枚举"""
    FIELD = "FIELD"
    OPERATOR = "OPERATOR"
    VALUE = "VALUE"
    LOGICAL = "LOGICAL"
    LPAREN = "LPAREN"
    RPAREN = "RPAREN"
    EOF = "EOF"


class Token:
    """Token类，表示解析过程中的一个词法单元"""
    
    def __init__(self, type_: TokenType, value: str, position: int = 0):
        self.type = type_
        self.value = value
        self.position = position
    
    def __repr__(self):
        return f"Token({self.type}, {self.value})"


class FilterParser:
    """过滤规则解析器类"""
    
    # 比较运算符映射
    OPERATORS = {
        '=': 'equals',
        '==': 'equals',
        '>': 'greater_than',
        '<': 'less_than',
        '>=': 'greater_equal',
        '<=': 'less_equal',
        'IN': 'in',
        'NOT IN': 'not_in',
        'CONTAINS': 'contains'
    }
    
    # 逻辑运算符
    LOGICAL_OPERATORS = {'AND', 'OR', 'NOT'}
    
    def __init__(self):
        """初始化过滤规则解析器"""
        self.data_accessor = DataAccessor()
        self.logger = logging.getLogger(__name__)
        self.tokens = []
        self.current_token_index = 0
        self.current_token = None
    
    def tokenize(self, expression: str) -> List[Token]:
        """
        将表达式分解为Token列表
        
        参数:
            expression: 过滤表达式字符串
            
        返回:
            Token列表
        """
        tokens = []
        i = 0
        
        while i < len(expression):
            char = expression[i]
            
            # 跳过空白字符
            if char.isspace():
                i += 1
                continue
            
            # 处理括号
            if char == '(':
                tokens.append(Token(TokenType.LPAREN, char, i))
                i += 1
            elif char == ')':
                tokens.append(Token(TokenType.RPAREN, char, i))
                i += 1
            
            # 处理字符串值（双引号或单引号）
            elif char in ['"', "'"]:
                quote_char = char
                start_pos = i
                i += 1
                value = ""
                
                while i < len(expression) and expression[i] != quote_char:
                    if expression[i] == '\\' and i + 1 < len(expression):
                        # 处理转义字符
                        i += 1
                        value += expression[i]
                    else:
                        value += expression[i]
                    i += 1
                
                if i < len(expression):
                    i += 1  # 跳过结束引号
                
                tokens.append(Token(TokenType.VALUE, value, start_pos))
            
            # 处理数字
            elif char.isdigit() or char == '.':
                start_pos = i
                value = ""
                
                while i < len(expression) and (expression[i].isdigit() or expression[i] == '.'):
                    value += expression[i]
                    i += 1
                
                tokens.append(Token(TokenType.VALUE, value, start_pos))
            
            # 处理运算符和关键字
            else:
                start_pos = i
                value = ""
                
                # 读取连续的非空白、非括号字符
                while (i < len(expression) and 
                       not expression[i].isspace() and 
                       expression[i] not in ['(', ')', '"', "'"]):
                    value += expression[i]
                    i += 1
                
                # 判断Token类型
                upper_value = value.upper()

                # 优先检查 NOT IN 运算符
                if upper_value == 'NOT' and i < len(expression):
                    # 检查是否是 "NOT IN"
                    remaining = expression[i:].lstrip()
                    if remaining.upper().startswith('IN'):
                        tokens.append(Token(TokenType.OPERATOR, 'NOT IN', start_pos))
                        # 跳过空格和 "IN"
                        spaces_count = len(expression[i:]) - len(remaining)
                        i += spaces_count + 2  # 跳过空格和 "IN"
                    else:
                        tokens.append(Token(TokenType.LOGICAL, 'NOT', start_pos))
                elif upper_value in self.LOGICAL_OPERATORS:
                    tokens.append(Token(TokenType.LOGICAL, upper_value, start_pos))
                elif upper_value in self.OPERATORS or value in ['=', '>', '<', '>=', '<=']:
                    tokens.append(Token(TokenType.OPERATOR, upper_value if upper_value in self.OPERATORS else value, start_pos))
                else:
                    # 默认作为字段名
                    tokens.append(Token(TokenType.FIELD, value, start_pos))
        
        tokens.append(Token(TokenType.EOF, '', len(expression)))
        return tokens

    def parse(self, expression: str) -> Dict[str, Any]:
        """
        解析过滤表达式

        参数:
            expression: 过滤表达式字符串

        返回:
            解析后的表达式树
        """
        try:
            self.tokens = self.tokenize(expression)
            self.current_token_index = 0
            self.current_token = self.tokens[0] if self.tokens else None

            result = self._parse_or_expression()

            if self.current_token.type != TokenType.EOF:
                raise ValueError(f"表达式解析未完成，剩余部分: {self.current_token.value}")

            return result

        except Exception as e:
            self.logger.error(f"解析表达式失败: {expression}, 错误: {e}")
            raise ValueError(f"表达式解析错误: {e}")

    def _advance(self) -> None:
        """移动到下一个Token"""
        if self.current_token_index < len(self.tokens) - 1:
            self.current_token_index += 1
            self.current_token = self.tokens[self.current_token_index]

    def _parse_or_expression(self) -> Dict[str, Any]:
        """解析OR表达式（最低优先级）"""
        left = self._parse_and_expression()

        while self.current_token.type == TokenType.LOGICAL and self.current_token.value == 'OR':
            self._advance()  # 跳过OR
            right = self._parse_and_expression()
            left = {
                'type': 'logical',
                'operator': 'OR',
                'left': left,
                'right': right
            }

        return left

    def _parse_and_expression(self) -> Dict[str, Any]:
        """解析AND表达式"""
        left = self._parse_not_expression()

        while self.current_token.type == TokenType.LOGICAL and self.current_token.value == 'AND':
            self._advance()  # 跳过AND
            right = self._parse_not_expression()
            left = {
                'type': 'logical',
                'operator': 'AND',
                'left': left,
                'right': right
            }

        return left

    def _parse_not_expression(self) -> Dict[str, Any]:
        """解析NOT表达式"""
        if self.current_token.type == TokenType.LOGICAL and self.current_token.value == 'NOT':
            self._advance()  # 跳过NOT
            operand = self._parse_primary_expression()
            return {
                'type': 'logical',
                'operator': 'NOT',
                'operand': operand
            }

        return self._parse_primary_expression()

    def _parse_primary_expression(self) -> Dict[str, Any]:
        """解析基本表达式（比较表达式或括号表达式）"""
        if self.current_token.type == TokenType.LPAREN:
            self._advance()  # 跳过左括号
            result = self._parse_or_expression()

            if self.current_token.type != TokenType.RPAREN:
                raise ValueError("缺少右括号")

            self._advance()  # 跳过右括号
            return result

        return self._parse_comparison_expression()

    def _parse_comparison_expression(self) -> Dict[str, Any]:
        """解析比较表达式"""
        if self.current_token.type != TokenType.FIELD:
            raise ValueError(f"期望字段名，但得到: {self.current_token.value}")

        field = self.current_token.value
        self._advance()

        if self.current_token.type != TokenType.OPERATOR:
            raise ValueError(f"期望运算符，但得到: {self.current_token.value}")

        operator = self.current_token.value
        self._advance()

        # 处理IN和NOT IN运算符的值列表
        if operator in ['IN', 'NOT IN']:
            if self.current_token.type != TokenType.LPAREN:
                raise ValueError(f"{operator}运算符后应该跟括号")

            self._advance()  # 跳过左括号
            values = []

            while self.current_token.type == TokenType.VALUE:
                values.append(self._convert_value(self.current_token.value))
                self._advance()

                # 检查是否有逗号分隔
                if (self.current_token.type == TokenType.FIELD and
                    self.current_token.value == ','):
                    self._advance()  # 跳过逗号

            if self.current_token.type != TokenType.RPAREN:
                raise ValueError("IN运算符的值列表缺少右括号")

            self._advance()  # 跳过右括号

            return {
                'type': 'comparison',
                'field': field,
                'operator': operator,
                'value': values
            }
        else:
            # 处理单个值的比较
            if self.current_token.type != TokenType.VALUE:
                raise ValueError(f"期望值，但得到: {self.current_token.value}")

            value = self._convert_value(self.current_token.value)
            self._advance()

            return {
                'type': 'comparison',
                'field': field,
                'operator': operator,
                'value': value
            }

    def _convert_value(self, value_str: str) -> Union[str, int, float]:
        """
        将字符串值转换为适当的类型

        参数:
            value_str: 字符串值

        返回:
            转换后的值
        """
        # 尝试转换为整数
        try:
            return int(value_str)
        except ValueError:
            pass

        # 尝试转换为浮点数
        try:
            return float(value_str)
        except ValueError:
            pass

        # 保持字符串
        return value_str

    def evaluate(self, expression_tree: Dict[str, Any], data: dict) -> bool:
        """
        对数据执行过滤表达式求值

        参数:
            expression_tree: 解析后的表达式树
            data: 要过滤的数据

        返回:
            过滤结果（True表示通过过滤，False表示被过滤掉）
        """
        try:
            return self._evaluate_node(expression_tree, data)
        except Exception as e:
            self.logger.error(f"表达式求值失败: {e}")
            return False

    def _evaluate_node(self, node: Dict[str, Any], data: dict) -> bool:
        """
        递归求值表达式树节点

        参数:
            node: 表达式树节点
            data: 数据

        返回:
            求值结果
        """
        node_type = node.get('type')

        if node_type == 'logical':
            return self._evaluate_logical_node(node, data)
        elif node_type == 'comparison':
            return self._evaluate_comparison_node(node, data)
        else:
            raise ValueError(f"未知的节点类型: {node_type}")

    def _evaluate_logical_node(self, node: Dict[str, Any], data: dict) -> bool:
        """求值逻辑运算节点"""
        operator = node['operator']

        if operator == 'AND':
            left_result = self._evaluate_node(node['left'], data)
            if not left_result:
                return False  # 短路求值
            return self._evaluate_node(node['right'], data)

        elif operator == 'OR':
            left_result = self._evaluate_node(node['left'], data)
            if left_result:
                return True  # 短路求值
            return self._evaluate_node(node['right'], data)

        elif operator == 'NOT':
            return not self._evaluate_node(node['operand'], data)

        else:
            raise ValueError(f"未知的逻辑运算符: {operator}")

    def _evaluate_comparison_node(self, node: Dict[str, Any], data: dict) -> bool:
        """求值比较运算节点"""
        field = node['field']
        operator = node['operator']
        expected_value = node['value']

        # 获取字段值
        actual_value = self.data_accessor.get_field_value(data, field)

        # 检查是否是数组访问结果
        if isinstance(actual_value, ArrayAccessResult):
            return self._evaluate_array_comparison(actual_value, operator, expected_value)

        # 执行普通比较运算
        if operator == '=' or operator == 'equals':
            return self._compare_equals(actual_value, expected_value)

        elif operator == '>':
            return self._compare_greater_than(actual_value, expected_value)

        elif operator == '<':
            return self._compare_less_than(actual_value, expected_value)

        elif operator == '>=':
            return self._compare_greater_equal(actual_value, expected_value)

        elif operator == '<=':
            return self._compare_less_equal(actual_value, expected_value)

        elif operator == 'IN':
            return self._compare_in(actual_value, expected_value)

        elif operator == 'NOT IN':
            return not self._compare_in(actual_value, expected_value)

        elif operator == 'CONTAINS':
            return self._compare_contains(actual_value, expected_value)

        else:
            raise ValueError(f"未知的比较运算符: {operator}")

    def _evaluate_array_comparison(self, array_result: ArrayAccessResult, operator: str, expected_value: Any) -> bool:
        """
        对数组访问结果执行比较运算

        参数:
            array_result: 数组访问结果对象
            operator: 比较运算符
            expected_value: 期望值

        返回:
            比较结果
        """
        values = array_result.get_values()
        access_mode = array_result.access_mode

        # 空数组的处理
        if not values:
            if access_mode == 'ANY':
                return False  # 没有元素满足条件
            else:  # ALL模式
                return True   # 所有元素都满足条件（空集合的全称量化为真）

        # 对每个值执行比较
        comparison_results = []
        for value in values:
            if value is None:
                # None值被视为不满足任何条件
                comparison_results.append(False)
                continue

            try:
                if operator == '=' or operator == 'equals':
                    result = self._compare_equals(value, expected_value)
                elif operator == '>':
                    result = self._compare_greater_than(value, expected_value)
                elif operator == '<':
                    result = self._compare_less_than(value, expected_value)
                elif operator == '>=':
                    result = self._compare_greater_equal(value, expected_value)
                elif operator == '<=':
                    result = self._compare_less_equal(value, expected_value)
                elif operator == 'IN':
                    result = self._compare_in(value, expected_value)
                elif operator == 'NOT IN':
                    result = self._compare_not_in(value, expected_value)
                elif operator == 'CONTAINS':
                    result = self._compare_contains(value, expected_value)
                else:
                    self.logger.warning(f"数组比较不支持的运算符: {operator}")
                    result = False

                comparison_results.append(result)

            except Exception as e:
                self.logger.debug(f"数组元素比较失败: {e}")
                comparison_results.append(False)

        # 根据访问模式返回结果
        if access_mode == 'ANY':
            return any(comparison_results)  # 任意一个为True即可
        else:  # ALL模式
            return all(comparison_results)  # 所有都必须为True

    def _compare_equals(self, actual: Any, expected: Any) -> bool:
        """等于比较"""
        if actual is None:
            return expected is None

        # 类型转换后比较
        actual_converted = self.data_accessor.convert_to_comparable(actual)
        expected_converted = self.data_accessor.convert_to_comparable(expected)

        return actual_converted == expected_converted

    def _compare_greater_than(self, actual: Any, expected: Any) -> bool:
        """大于比较"""
        if actual is None:
            return False

        try:
            actual_converted = self.data_accessor.convert_to_comparable(actual)
            expected_converted = self.data_accessor.convert_to_comparable(expected)

            if isinstance(actual_converted, (int, float)) and isinstance(expected_converted, (int, float)):
                return actual_converted > expected_converted
            elif isinstance(actual_converted, str) and isinstance(expected_converted, str):
                return actual_converted > expected_converted
            else:
                return False
        except (TypeError, ValueError):
            return False

    def _compare_less_than(self, actual: Any, expected: Any) -> bool:
        """小于比较"""
        if actual is None:
            return False

        try:
            actual_converted = self.data_accessor.convert_to_comparable(actual)
            expected_converted = self.data_accessor.convert_to_comparable(expected)

            if isinstance(actual_converted, (int, float)) and isinstance(expected_converted, (int, float)):
                return actual_converted < expected_converted
            elif isinstance(actual_converted, str) and isinstance(expected_converted, str):
                return actual_converted < expected_converted
            else:
                return False
        except (TypeError, ValueError):
            return False

    def _compare_greater_equal(self, actual: Any, expected: Any) -> bool:
        """大于等于比较"""
        return self._compare_greater_than(actual, expected) or self._compare_equals(actual, expected)

    def _compare_less_equal(self, actual: Any, expected: Any) -> bool:
        """小于等于比较"""
        return self._compare_less_than(actual, expected) or self._compare_equals(actual, expected)

    def _compare_in(self, actual: Any, expected_list: List[Any]) -> bool:
        """IN比较"""
        if actual is None:
            return None in expected_list

        actual_converted = self.data_accessor.convert_to_comparable(actual)

        for expected in expected_list:
            expected_converted = self.data_accessor.convert_to_comparable(expected)
            if actual_converted == expected_converted:
                return True

        return False

    def _compare_not_in(self, actual: Any, expected_list: List[Any]) -> bool:
        """NOT IN比较"""
        return not self._compare_in(actual, expected_list)

    def _compare_contains(self, actual: Any, expected: Any) -> bool:
        """包含比较"""
        if actual is None:
            return False

        actual_str = str(actual)
        expected_str = str(expected)

        return expected_str in actual_str

    def filter_data(self, expression: str, data: dict) -> bool:
        """
        对单条数据执行过滤

        参数:
            expression: 过滤表达式字符串
            data: 要过滤的数据

        返回:
            过滤结果
        """
        try:
            expression_tree = self.parse(expression)
            return self.evaluate(expression_tree, data)
        except Exception as e:
            self.logger.error(f"过滤数据失败: {e}")
            return False
