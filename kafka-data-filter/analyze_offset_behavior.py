#!/usr/bin/env python3
"""
分析Kafka消费者offset行为的脚本

演示auto_offset_reset配置的实际生效条件和机制
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager


def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


def analyze_consumer_group_offset_behavior():
    """分析消费者组的offset行为"""
    logger = setup_logging()
    
    print("📊 分析Kafka消费者组offset行为")
    print("=" * 80)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        
        group_id = kafka_config.get('group_id', 'moye-check-data')
        topic = kafka_config.get('topic', 'moyeT_dfycb_website')
        auto_offset_reset = kafka_config.get('auto_offset_reset', 'latest')
        enable_auto_commit = kafka_config.get('enable_auto_commit', True)
        
        print(f"消费者组ID: {group_id}")
        print(f"Topic: {topic}")
        print(f"auto_offset_reset: {auto_offset_reset}")
        print(f"enable_auto_commit: {enable_auto_commit}")
        
        # 创建消费者组管理器
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        print("\n1. 检查消费者组当前状态:")
        print("-" * 50)
        
        # 检查消费者组是否存在
        exists, check_info = group_manager.consumer_group_exists(group_id)
        print(f"消费者组存在: {exists}")
        print(f"检查信息: {check_info}")
        
        if exists:
            # 获取详细信息
            group_info = group_manager.get_consumer_group_info(group_id)
            
            if group_info.get('exists', False):
                print(f"消费者组状态: {group_info.get('state', 'Unknown')}")
                print(f"成员数量: {group_info.get('member_count', 0)}")
                
                # 分析offset信息
                offsets = group_info.get('offsets', {})
                if offsets:
                    print(f"\n已提交的offset信息:")
                    total_lag = 0
                    for partition_key, offset_data in offsets.items():
                        current_offset = offset_data.get('current_offset')
                        end_offset = offset_data.get('end_offset')
                        lag = offset_data.get('lag', 0) or 0
                        total_lag += lag
                        
                        print(f"  {partition_key}:")
                        print(f"    当前offset: {current_offset}")
                        print(f"    最新offset: {end_offset}")
                        print(f"    Lag: {lag}")
                    
                    print(f"\n总Lag: {total_lag}")
                    
                    # 分析auto_offset_reset的生效情况
                    print(f"\n2. auto_offset_reset配置分析:")
                    print("-" * 50)
                    
                    if any(offset_data.get('current_offset') is not None for offset_data in offsets.values()):
                        print("✅ 消费者组有有效的提交offset")
                        print(f"❌ auto_offset_reset='{auto_offset_reset}' 不会生效")
                        print("📝 原因: Kafka优先使用已提交的offset")
                        print("📝 行为: 程序将从上次提交的offset位置继续消费")
                        
                        if auto_offset_reset == 'latest':
                            print("\n💡 如果想从最新消息开始消费，需要:")
                            print("   1. 删除消费者组（推荐使用自动清理功能）")
                            print("   2. 或者使用不同的消费者组ID")
                            print("   3. 或者手动重置offset到最新位置")
                    else:
                        print("⚠️  消费者组存在但没有有效的提交offset")
                        print(f"✅ auto_offset_reset='{auto_offset_reset}' 将会生效")
                else:
                    print("⚠️  无法获取offset信息")
            else:
                print("❌ 无法获取消费者组详细信息")
        else:
            print("✅ 消费者组不存在")
            print(f"✅ auto_offset_reset='{auto_offset_reset}' 将在首次启动时生效")
            print("📝 行为: 程序首次启动时将从最新消息开始消费")
        
        print(f"\n3. enable_auto_commit配置分析:")
        print("-" * 50)
        
        if enable_auto_commit:
            print("✅ enable_auto_commit=true")
            print("📝 行为: 消费者会自动定期提交offset")
            print("📝 影响: 程序重启后会从上次自动提交的位置继续")
            print("📝 优点: 避免消息重复处理")
            print("📝 缺点: 无法每次都从最新消息开始")
        else:
            print("✅ enable_auto_commit=false")
            print("📝 行为: 需要手动提交offset")
            print("📝 影响: 如果不手动提交，重启后可能重复处理消息")
        
        print(f"\n4. 当前配置的实际行为总结:")
        print("-" * 50)
        
        if exists:
            print("🔄 程序启动行为:")
            print("   1. 检查消费者组是否存在 → 存在")
            print("   2. 检查是否有提交的offset → 有")
            print("   3. 忽略auto_offset_reset配置")
            print("   4. 从上次提交的offset位置继续消费")
            print("   5. 自动提交新的offset")
            
            print("\n🎯 这是Kafka的正常行为，确保:")
            print("   ✅ 消息不丢失")
            print("   ✅ 消息不重复处理")
            print("   ✅ 消费进度持久化")
        else:
            print("🆕 程序首次启动行为:")
            print("   1. 检查消费者组是否存在 → 不存在")
            print("   2. 创建新的消费者组")
            print("   3. 应用auto_offset_reset='latest'配置")
            print("   4. 从最新消息开始消费")
            print("   5. 自动提交offset")
        
        group_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_offset_reset_solutions():
    """演示如何实现每次从最新消息开始消费的解决方案"""
    print("\n💡 如何实现每次从最新消息开始消费")
    print("=" * 80)
    
    print("方案1: 使用自动清理功能（推荐）")
    print("-" * 50)
    print("✅ 优点: 自动化，无需手动干预")
    print("✅ 配置: auto_cleanup.enabled = true（已配置）")
    print("✅ 行为: 程序退出时自动删除消费者组")
    print("✅ 结果: 下次启动时auto_offset_reset='latest'生效")
    
    print("\n方案2: 使用不同的消费者组ID")
    print("-" * 50)
    print("✅ 优点: 简单直接")
    print("✅ 方法: 每次启动使用不同的group_id")
    print("✅ 示例: group_id: 'moye-check-data-{timestamp}'")
    print("⚠️  缺点: 会产生很多无用的消费者组")
    
    print("\n方案3: 手动重置offset")
    print("-" * 50)
    print("✅ 优点: 精确控制")
    print("✅ 方法: 使用kafka-consumer-groups.sh工具")
    print("✅ 命令示例:")
    print("   kafka-consumer-groups.sh --bootstrap-server 123.235.23.238:19301 \\")
    print("     --group moye-check-data --reset-offsets --to-latest \\")
    print("     --topic moyeT_dfycb_website --execute")
    
    print("\n方案4: 修改代码逻辑")
    print("-" * 50)
    print("✅ 优点: 完全控制消费行为")
    print("✅ 方法: 在代码中手动设置消费位置")
    print("✅ 实现: 使用consumer.seek_to_end()方法")
    
    print("\n🎯 推荐方案:")
    print("=" * 50)
    print("对于您的使用场景，推荐使用 方案1（自动清理功能）:")
    print("1. 已经配置了auto_cleanup.enabled = true")
    print("2. 程序退出时会自动删除消费者组")
    print("3. 下次启动时会从最新消息开始消费")
    print("4. 无需手动干预，完全自动化")
    
    print("\n📝 验证方法:")
    print("1. 运行程序: python main.py filter_config.yaml")
    print("2. 停止程序（Ctrl+C）")
    print("3. 查看日志确认消费者组已被删除")
    print("4. 再次启动程序")
    print("5. 观察是否从最新消息开始消费")


def main():
    """主函数"""
    print("🔍 Kafka消费者offset行为分析工具")
    print("=" * 80)
    
    try:
        # 分析当前offset行为
        success = analyze_consumer_group_offset_behavior()
        
        if success:
            # 演示解决方案
            demonstrate_offset_reset_solutions()
            
            print("\n✅ 分析完成！")
            print("\n📋 总结:")
            print("1. auto_offset_reset='latest'的行为是正常的")
            print("2. 当消费者组存在时，优先使用已提交的offset")
            print("3. 自动清理功能可以解决您的需求")
            print("4. 每次程序退出后消费者组会被删除")
            print("5. 下次启动时会从最新消息开始消费")
            
            return True
        else:
            print("❌ 分析失败")
            return False
            
    except KeyboardInterrupt:
        print("\n分析被用户中断")
        return False
    except Exception as e:
        print(f"分析执行失败: {e}")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"程序执行失败: {e}")
        sys.exit(1)
