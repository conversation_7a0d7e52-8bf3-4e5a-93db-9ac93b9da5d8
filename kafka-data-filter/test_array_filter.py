#!/usr/bin/env python3
"""
测试数组过滤功能的脚本
验证新的数组访问语法和比较逻辑
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

from kafka_data_filter.data_accessor import DataAccessor, ArrayAccessResult
from kafka_data_filter.filter_parser import FilterParser

def test_array_access_result():
    """测试ArrayAccessResult类"""
    print("=== 测试ArrayAccessResult类 ===")
    
    test_data = [
        {"class2": "广告信息", "probability": "0.9", "id": 1},
        {"class2": "其他", "probability": "0.8", "id": 2},
        {"class2": "新闻资讯", "probability": "0.7", "id": 3}
    ]
    
    # 测试ANY模式
    result_any = ArrayAccessResult(test_data, "class2", "ANY")
    values = result_any.get_values()
    print(f"ANY模式获取class2值: {values}")
    
    # 测试ALL模式
    result_all = ArrayAccessResult(test_data, "probability", "ALL")
    prob_values = result_all.get_values()
    print(f"ALL模式获取probability值: {prob_values}")
    
    print(f"ArrayAccessResult对象: {result_any}")

def test_data_accessor_array_syntax():
    """测试DataAccessor的数组语法支持"""
    print("\n=== 测试DataAccessor数组语法 ===")
    
    test_data = {
        "n_content_spam": [
            {"class2": "广告信息", "probability": "0.9", "id": 1},
            {"class2": "其他", "probability": "0.8", "id": 2}
        ],
        "y_yq_focus": {
            "label_name": "关注",
            "probability": 0.9
        }
    }
    
    accessor = DataAccessor()
    
    # 测试新的数组语法
    print("1. 测试新的数组访问语法:")
    result1 = accessor.get_field_value(test_data, "n_content_spam[ANY].class2")
    print(f"n_content_spam[ANY].class2 = {result1}")
    print(f"类型: {type(result1)}")
    
    result2 = accessor.get_field_value(test_data, "n_content_spam[ALL].probability")
    print(f"n_content_spam[ALL].probability = {result2}")
    print(f"类型: {type(result2)}")
    
    # 测试向后兼容的语法
    print("\n2. 测试向后兼容语法:")
    result3 = accessor.get_field_value(test_data, "n_content_spam.class2")
    print(f"n_content_spam.class2 = {result3}")
    print(f"类型: {type(result3)}")
    
    # 测试传统索引访问
    print("\n3. 测试传统索引访问:")
    result4 = accessor.get_field_value(test_data, "n_content_spam.0.class2")
    print(f"n_content_spam.0.class2 = {result4}")
    
    # 测试普通字段访问
    print("\n4. 测试普通字段访问:")
    result5 = accessor.get_field_value(test_data, "y_yq_focus.label_name")
    print(f"y_yq_focus.label_name = {result5}")

def test_array_filtering():
    """测试数组过滤功能"""
    print("\n=== 测试数组过滤功能 ===")
    
    # 测试数据1: 混合情况
    test_data1 = {
        "n_content_spam": [
            {"class2": "广告信息", "probability": "0.9", "id": 1},
            {"class2": "其他", "probability": "0.8", "id": 2}
        ]
    }
    
    # 测试数据2: 全部符合
    test_data2 = {
        "n_content_spam": [
            {"class2": "其他", "probability": "0.9", "id": 1},
            {"class2": "新闻资讯", "probability": "0.8", "id": 2}
        ]
    }
    
    # 测试数据3: 全部不符合
    test_data3 = {
        "n_content_spam": [
            {"class2": "广告信息", "probability": "0.9", "id": 1},
            {"class2": "股票资讯", "probability": "0.8", "id": 2}
        ]
    }
    
    parser = FilterParser()
    
    print("1. 测试ANY模式过滤:")
    rule_any = 'n_content_spam[ANY].class2 NOT IN ("广告信息", "股票资讯", "新闻合集")'
    
    result1 = parser.filter_data(rule_any, test_data1)
    print(f"混合数据 + ANY模式: {result1} (期望: True)")
    
    result2 = parser.filter_data(rule_any, test_data2)
    print(f"全部符合 + ANY模式: {result2} (期望: True)")
    
    result3 = parser.filter_data(rule_any, test_data3)
    print(f"全部不符合 + ANY模式: {result3} (期望: False)")
    
    print("\n2. 测试ALL模式过滤:")
    rule_all = 'n_content_spam[ALL].class2 NOT IN ("广告信息", "股票资讯", "新闻合集")'
    
    result4 = parser.filter_data(rule_all, test_data1)
    print(f"混合数据 + ALL模式: {result4} (期望: False)")
    
    result5 = parser.filter_data(rule_all, test_data2)
    print(f"全部符合 + ALL模式: {result5} (期望: True)")
    
    result6 = parser.filter_data(rule_all, test_data3)
    print(f"全部不符合 + ALL模式: {result6} (期望: False)")
    
    print("\n3. 测试向后兼容模式:")
    rule_compat = 'n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集")'
    
    result7 = parser.filter_data(rule_compat, test_data1)
    print(f"混合数据 + 兼容模式: {result7} (期望: True, 等同于ANY)")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    parser = FilterParser()
    
    # 空数组
    empty_data = {"n_content_spam": []}
    rule = 'n_content_spam[ANY].class2 = "其他"'
    result1 = parser.filter_data(rule, empty_data)
    print(f"空数组 + ANY模式: {result1} (期望: False)")
    
    rule_all = 'n_content_spam[ALL].class2 = "其他"'
    result2 = parser.filter_data(rule_all, empty_data)
    print(f"空数组 + ALL模式: {result2} (期望: True)")
    
    # 字段不存在
    missing_field_data = {
        "n_content_spam": [
            {"class1": "垃圾文", "id": 1},  # 缺少class2字段
            {"class2": "其他", "id": 2}
        ]
    }
    rule_missing = 'n_content_spam[ALL].class2 = "其他"'
    result3 = parser.filter_data(rule_missing, missing_field_data)
    print(f"字段缺失 + ALL模式: {result3} (期望: False)")

if __name__ == "__main__":
    test_array_access_result()
    test_data_accessor_array_syntax()
    test_array_filtering()
    test_edge_cases()
    print("\n=== 测试完成 ===")
