#!/usr/bin/env python3
"""
Kafka消费者组管理命令行工具

提供消费者组的查看、监控、删除等管理功能
支持安全检查和批量操作
"""

import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager
from kafka_data_filter.config_manager import ConfigManager


class ConsumerGroupAdmin:
    """消费者组管理命令行工具"""
    
    def __init__(self, config_path: str = None, bootstrap_servers: str = None):
        """
        初始化管理工具
        
        参数:
            config_path: 配置文件路径
            bootstrap_servers: Kafka集群地址（直接指定）
        """
        self.logger = self._setup_logging()
        
        if config_path:
            # 从配置文件加载
            self.config_manager = ConfigManager(config_path)
            kafka_config = self.config_manager.get_kafka_config()
            self.bootstrap_servers = kafka_config['bootstrap_servers']
            self.kafka_config = kafka_config
        elif bootstrap_servers:
            # 直接使用指定的服务器地址
            self.bootstrap_servers = bootstrap_servers
            self.kafka_config = {'bootstrap_servers': bootstrap_servers}
        else:
            raise ValueError("必须提供配置文件路径或Kafka服务器地址")
        
        # 初始化消费者组管理器
        self.manager = KafkaConsumerGroupManager(
            bootstrap_servers=self.bootstrap_servers,
            **{k: v for k, v in self.kafka_config.items() if k != 'bootstrap_servers'}
        )
        
        self.logger.info(f"连接到Kafka集群: {self.bootstrap_servers}")
    
    def _setup_logging(self) -> logging.Logger:
        """配置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        return logging.getLogger(__name__)
    
    def list_groups(self, output_format: str = 'table') -> None:
        """
        列出所有消费者组
        
        参数:
            output_format: 输出格式 (table, json)
        """
        try:
            self.logger.info("获取消费者组列表...")
            groups = self.manager.list_all_consumer_groups()
            
            if not groups:
                print("未找到任何消费者组")
                return
            
            if output_format == 'json':
                print(json.dumps(groups, indent=2, ensure_ascii=False))
            else:
                self._print_groups_table(groups)
                
        except Exception as e:
            self.logger.error(f"列出消费者组失败: {e}")
            sys.exit(1)
    
    def _print_groups_table(self, groups: List[Dict[str, Any]]) -> None:
        """以表格形式打印消费者组信息"""
        print("\n" + "=" * 100)
        print(f"{'消费者组ID':<20} {'状态':<10} {'成员数':<8} {'分区数':<8} {'总Lag':<10} {'协调器':<20}")
        print("=" * 100)
        
        for group in groups:
            if not group.get('exists', False):
                continue
                
            group_id = group.get('group_id', 'Unknown')
            state = group.get('state', 'Unknown')
            member_count = group.get('member_count', 0)
            
            offsets = group.get('offsets', {})
            partition_count = len(offsets)
            total_lag = sum(offset.get('lag', 0) or 0 for offset in offsets.values())
            
            coordinator = group.get('coordinator', {})
            coordinator_info = f"{coordinator.get('host', 'Unknown')}:{coordinator.get('port', 'Unknown')}"
            
            print(f"{group_id:<20} {state:<10} {member_count:<8} {partition_count:<8} {total_lag:<10} {coordinator_info:<20}")
        
        print("=" * 100)
    
    def describe_group(self, group_id: str, output_format: str = 'detailed') -> None:
        """
        描述指定消费者组的详细信息
        
        参数:
            group_id: 消费者组ID
            output_format: 输出格式 (detailed, json)
        """
        try:
            self.logger.info(f"获取消费者组 {group_id} 的详细信息...")
            group_info = self.manager.get_consumer_group_info(group_id)
            
            if not group_info.get('exists', False):
                print(f"消费者组 {group_id} 不存在")
                return
            
            if output_format == 'json':
                print(json.dumps(group_info, indent=2, ensure_ascii=False))
            else:
                self._print_group_details(group_info)
                
        except Exception as e:
            self.logger.error(f"获取消费者组信息失败: {e}")
            sys.exit(1)
    
    def _print_group_details(self, group_info: Dict[str, Any]) -> None:
        """打印消费者组详细信息"""
        print(f"\n消费者组详细信息: {group_info['group_id']}")
        print("=" * 60)
        print(f"状态: {group_info.get('state', 'Unknown')}")
        print(f"协议类型: {group_info.get('protocol_type', 'Unknown')}")
        print(f"协议: {group_info.get('protocol', 'Unknown')}")
        print(f"成员数量: {group_info.get('member_count', 0)}")
        
        coordinator = group_info.get('coordinator', {})
        print(f"协调器: {coordinator.get('host')}:{coordinator.get('port')} (ID: {coordinator.get('id')})")
        
        # 打印成员信息
        members = group_info.get('members', [])
        if members:
            print(f"\n成员信息 ({len(members)} 个):")
            print("-" * 60)
            for i, member in enumerate(members, 1):
                print(f"  成员 {i}:")
                print(f"    成员ID: {member.get('member_id', 'Unknown')}")
                print(f"    客户端ID: {member.get('client_id', 'Unknown')}")
                print(f"    客户端主机: {member.get('client_host', 'Unknown')}")
        
        # 打印offset信息
        offsets = group_info.get('offsets', {})
        if offsets:
            print(f"\nOffset信息 ({len(offsets)} 个分区):")
            print("-" * 60)
            total_lag = 0
            for partition_key, offset_data in offsets.items():
                topic = offset_data.get('topic', 'Unknown')
                partition = offset_data.get('partition', 'Unknown')
                current_offset = offset_data.get('current_offset', 'Unknown')
                end_offset = offset_data.get('end_offset', 'Unknown')
                lag = offset_data.get('lag', 0) or 0
                total_lag += lag
                
                print(f"  {topic}-{partition}:")
                print(f"    当前Offset: {current_offset}")
                print(f"    最新Offset: {end_offset}")
                print(f"    Lag: {lag}")
            
            print(f"\n总Lag: {total_lag}")
        
        print("=" * 60)
    
    def check_group_safety(self, group_id: str) -> None:
        """
        检查消费者组删除安全性
        
        参数:
            group_id: 消费者组ID
        """
        try:
            self.logger.info(f"检查消费者组 {group_id} 的删除安全性...")
            is_safe, warnings = self.manager.check_group_safety_for_deletion(group_id)
            
            print(f"\n消费者组 {group_id} 安全检查结果:")
            print("=" * 50)
            print(f"是否安全删除: {'是' if is_safe else '否'}")
            
            if warnings:
                print("\n警告信息:")
                for i, warning in enumerate(warnings, 1):
                    print(f"  {i}. {warning}")
            
            if is_safe:
                print("\n✅ 该消费者组可以安全删除")
            else:
                print("\n❌ 该消费者组不建议立即删除")
                print("   建议先停止所有消费者实例，等待消费者组变为Empty状态")
            
            print("=" * 50)
            
        except Exception as e:
            self.logger.error(f"检查消费者组安全性失败: {e}")
            sys.exit(1)
    
    def delete_group(self, group_id: str, force: bool = False, 
                    no_backup: bool = False, wait_empty: bool = True) -> None:
        """
        删除消费者组
        
        参数:
            group_id: 消费者组ID
            force: 是否强制删除
            no_backup: 是否跳过备份
            wait_empty: 是否等待消费者组变为空
        """
        try:
            print(f"\n准备删除消费者组: {group_id}")
            print("=" * 50)
            
            # 等待消费者组变为空（如果需要）
            if wait_empty and not force:
                print("等待消费者组变为空状态...")
                if not self.manager.wait_for_group_empty(group_id, timeout_seconds=60):
                    print("❌ 消费者组未能在指定时间内变为空状态")
                    print("   使用 --force 参数可强制删除")
                    return
            
            # 执行删除
            success = self.manager.delete_consumer_group(
                group_id=group_id,
                force=force,
                backup=not no_backup
            )
            
            if success:
                print(f"✅ 消费者组 {group_id} 删除成功")
                
                # 验证删除结果
                if self.manager.verify_group_deletion(group_id):
                    print("✅ 删除验证通过")
                else:
                    print("⚠️  删除验证失败，请手动确认")
            else:
                print(f"❌ 消费者组 {group_id} 删除失败")
                sys.exit(1)
            
            print("=" * 50)
            
        except Exception as e:
            self.logger.error(f"删除消费者组失败: {e}")
            sys.exit(1)
    
    def backup_group(self, group_id: str, output_path: str = None) -> None:
        """
        备份消费者组信息
        
        参数:
            group_id: 消费者组ID
            output_path: 输出文件路径
        """
        try:
            self.logger.info(f"备份消费者组 {group_id} 的信息...")
            backup_path = self.manager.backup_consumer_group_offsets(group_id, output_path)
            print(f"✅ 消费者组信息已备份到: {backup_path}")
            
        except Exception as e:
            self.logger.error(f"备份消费者组信息失败: {e}")
            sys.exit(1)
    
    def close(self) -> None:
        """关闭管理工具"""
        if self.manager:
            self.manager.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Kafka消费者组管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 列出所有消费者组
  python consumer_group_admin.py --config filter_config.yaml list

  # 查看指定消费者组详情
  python consumer_group_admin.py --config filter_config.yaml describe moye-check-data

  # 检查消费者组删除安全性
  python consumer_group_admin.py --config filter_config.yaml check moye-check-data

  # 安全删除消费者组
  python consumer_group_admin.py --config filter_config.yaml delete moye-check-data

  # 强制删除消费者组（跳过安全检查）
  python consumer_group_admin.py --config filter_config.yaml delete moye-check-data --force

  # 备份消费者组信息
  python consumer_group_admin.py --config filter_config.yaml backup moye-check-data

  # 直接指定Kafka服务器地址
  python consumer_group_admin.py --bootstrap-servers localhost:9092 list
        """
    )

    # 连接配置参数
    connection_group = parser.add_mutually_exclusive_group(required=True)
    connection_group.add_argument(
        "--config", "-c",
        help="配置文件路径"
    )
    connection_group.add_argument(
        "--bootstrap-servers", "-b",
        help="Kafka集群地址 (例如: localhost:9092)"
    )

    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # list命令
    list_parser = subparsers.add_parser('list', help='列出所有消费者组')
    list_parser.add_argument(
        '--format', '-f',
        choices=['table', 'json'],
        default='table',
        help='输出格式'
    )

    # describe命令
    describe_parser = subparsers.add_parser('describe', help='查看消费者组详细信息')
    describe_parser.add_argument('group_id', help='消费者组ID')
    describe_parser.add_argument(
        '--format', '-f',
        choices=['detailed', 'json'],
        default='detailed',
        help='输出格式'
    )

    # check命令
    check_parser = subparsers.add_parser('check', help='检查消费者组删除安全性')
    check_parser.add_argument('group_id', help='消费者组ID')

    # delete命令
    delete_parser = subparsers.add_parser('delete', help='删除消费者组')
    delete_parser.add_argument('group_id', help='消费者组ID')
    delete_parser.add_argument(
        '--force',
        action='store_true',
        help='强制删除（跳过安全检查）'
    )
    delete_parser.add_argument(
        '--no-backup',
        action='store_true',
        help='跳过备份'
    )
    delete_parser.add_argument(
        '--no-wait',
        action='store_true',
        help='不等待消费者组变为空'
    )

    # backup命令
    backup_parser = subparsers.add_parser('backup', help='备份消费者组信息')
    backup_parser.add_argument('group_id', help='消费者组ID')
    backup_parser.add_argument(
        '--output', '-o',
        help='输出文件路径'
    )

    # 解析参数
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    # 创建管理工具实例
    try:
        if args.config:
            admin = ConsumerGroupAdmin(config_path=args.config)
        else:
            admin = ConsumerGroupAdmin(bootstrap_servers=args.bootstrap_servers)
    except Exception as e:
        print(f"初始化失败: {e}")
        sys.exit(1)

    try:
        # 执行相应命令
        if args.command == 'list':
            admin.list_groups(output_format=args.format)

        elif args.command == 'describe':
            admin.describe_group(args.group_id, output_format=args.format)

        elif args.command == 'check':
            admin.check_group_safety(args.group_id)

        elif args.command == 'delete':
            # 确认删除操作
            if not args.force:
                confirm = input(f"确定要删除消费者组 '{args.group_id}' 吗？(y/N): ")
                if confirm.lower() not in ['y', 'yes']:
                    print("操作已取消")
                    sys.exit(0)

            admin.delete_group(
                group_id=args.group_id,
                force=args.force,
                no_backup=args.no_backup,
                wait_empty=not args.no_wait
            )

        elif args.command == 'backup':
            admin.backup_group(args.group_id, args.output)

    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"执行命令失败: {e}")
        sys.exit(1)
    finally:
        admin.close()


if __name__ == "__main__":
    main()
