2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-22 17:01:32,641 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-22 17:01:32,641 - kafka_data_filter.output_handler - INFO - 生成的输出文件路径: output/filtered_messages_20250822_170132.json
2025-08-22 17:01:32,641 - kafka_data_filter.output_handler - INFO - 创建新的JSON数组文件: output/filtered_messages_20250822_170132.json
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-22 17:01:32,641 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-22 17:01:32,646 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:32,646 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:01:32,724 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:01:32,889 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:01:32,889 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:01:32,890 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:01:33,066 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:01:33,067 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:01:33,194 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:33,195 - kafka.conn - INFO - Probing node 0 broker version
2025-08-22 17:01:33,260 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:01:33,260 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:01:33,428 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:01:33,428 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:01:33,428 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-22 17:01:33,428 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-22 17:01:33,428 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-22 17:01:33,428 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-22 17:01:33,429 - kafka_data_filter.main - INFO - 自动清理管理器已初始化
2025-08-22 17:01:33,429 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-22 17:01:33,429 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-22 17:01:33,429 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-22 17:01:33,429 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:33,429 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:01:33,500 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:01:33,690 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:01:33,690 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:01:33,691 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-22 17:01:33,692 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-22 17:01:33,692 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x102775d00>}
2025-08-22 17:01:33,692 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-22 17:01:33,692 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-22 17:01:33,774 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-22 17:01:33,774 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-22 17:01:33,775 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-22 17:01:33,775 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-22 17:01:33,776 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:33,881 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:01:33,882 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:01:33,988 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-22 17:01:34,055 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-22 17:01:34,055 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:34,130 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 1
2025-08-22 17:01:34,130 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-22 17:01:34,130 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-22 17:01:34,130 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:02:28,678 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-22 17:03:02,758 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-22 17:03:50,392 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-22 17:04:31,345 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 400
2025-08-22 17:05:23,963 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 500
2025-08-22 17:06:10,368 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 600
2025-08-22 17:06:22,148 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-22 17:06:22,148 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-22 17:06:22,148 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 17:06:22,655 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 631, 错误数: 0
2025-08-22 17:06:22,656 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-22 17:06:22,656 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-22 17:06:22,656 - kafka_data_filter.main - INFO - 运行时间: 289.23 秒
消费消息数: 631
处理消息数: 631
通过过滤数: 3
被过滤数: 628
输出消息数: 3
错误数: 0
通过率: 0.48%
平均处理时间: 0.04 ms
当前吞吐量: 2.44 消息/秒
总体吞吐量: 2.18 消息/秒
2025-08-22 17:06:22,656 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-22 17:06:22,657 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 17:06:22,657 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-22 17:06:22,730 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-22 17:06:22,731 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-22 17:06:22,731 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-22 17:06:22,731 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-22 17:06:22,795 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-22 17:06:22,795 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-22 17:06:22,796 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:22,796 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:22,797 - kafka_data_filter.kafka_consumer - INFO - 消费者连接已关闭
2025-08-22 17:06:22,797 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-22 17:06:22,797 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-22 17:06:22,797 - kafka_data_filter.main - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-22 17:06:22,797 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-22 17:06:22,797 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-22 17:06:22,797 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-22 17:06:22,797 - kafka_data_filter.auto_cleanup_manager - INFO - 等待Kafka集群状态稳定...
2025-08-22 17:06:24,801 - kafka_data_filter.auto_cleanup_manager - INFO - 检查消费者组 moye-check-data 是否存在...
2025-08-22 17:06:24,867 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组存在性检查结果: exists=True, info=found_in_list
2025-08-22 17:06:24,867 - kafka_data_filter.auto_cleanup_manager - INFO - 获取消费者组详细信息进行二次确认...
2025-08-22 17:06:24,998 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:24,998 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:25,074 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:25,248 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:25,249 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:25,249 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:25,318 - kafka_data_filter.auto_cleanup_manager - INFO - 等待消费者组变为空状态...
2025-08-22 17:06:25,318 - kafka_data_filter.kafka_consumer_group_manager - INFO - 等待消费者组 moye-check-data 变为空状态...
2025-08-22 17:06:25,472 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:25,473 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:25,534 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:25,703 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:25,704 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:25,704 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:25,791 - kafka_data_filter.kafka_consumer_group_manager - INFO - 消费者组 moye-check-data 已变为空状态
2025-08-22 17:06:25,925 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:25,926 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:25,994 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:26,158 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:26,158 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:26,159 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:26,228 - kafka_data_filter.kafka_consumer_group_manager - INFO - 消费者组 moye-check-data 的信息已备份到: auto_cleanup_backup_moye-check-data_20250822_170625.json
2025-08-22 17:06:26,229 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组信息已备份到: auto_cleanup_backup_moye-check-data_20250822_170625.json
2025-08-22 17:06:26,229 - kafka_data_filter.auto_cleanup_manager - INFO - 删除消费者组: moye-check-data
2025-08-22 17:06:26,229 - kafka_data_filter.kafka_consumer_group_manager - INFO - 开始删除消费者组: moye-check-data
2025-08-22 17:06:26,415 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:26,415 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:26,487 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:26,663 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:26,663 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:26,664 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:26,732 - kafka_data_filter.kafka_consumer_group_manager - INFO - 正在删除消费者组: moye-check-data
2025-08-22 17:06:26,867 - kafka_data_filter.kafka_consumer_group_manager - WARNING - 删除结果检查不确定，尝试验证消费者组是否仍存在...
2025-08-22 17:06:29,107 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:29,108 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:29,172 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:29,343 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:29,343 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:29,344 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:29,409 - kafka_data_filter.kafka_consumer_group_manager - ERROR - 删除失败：消费者组 moye-check-data 仍然存在 (describe_result_unknown)
2025-08-22 17:06:29,409 - kafka_data_filter.auto_cleanup_manager - ERROR - ❌ 消费者组 moye-check-data 自动清理失败
2025-08-22 17:06:29,409 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:29,409 - kafka_data_filter.kafka_consumer_group_manager - INFO - Kafka管理客户端已关闭
2025-08-28 22:14:50,380 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-28 22:14:50,381 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-28 22:14:50,381 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-28 22:14:50,381 - __main__ - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-28 22:14:50,381 - kafka_data_filter.output_handler - INFO - 生成的输出文件路径: output/filtered_messages_20250828_221450.json
2025-08-28 22:14:50,381 - kafka_data_filter.output_handler - INFO - 创建新的JSON数组文件: output/filtered_messages_20250828_221450.json
2025-08-28 22:14:50,381 - __main__ - INFO - 输出处理器已初始化
2025-08-28 22:14:50,381 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-28 22:14:50,381 - __main__ - INFO - 统计模块已初始化
2025-08-28 22:14:50,381 - __main__ - INFO - Kafka消费者已初始化
2025-08-28 22:14:50,386 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:50,386 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:14:50,434 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:50,586 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:14:50,586 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:14:50,586 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:14:50,737 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:14:50,737 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:14:50,828 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:50,828 - kafka.conn - INFO - Probing node 0 broker version
2025-08-28 22:14:50,870 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:50,870 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:14:51,016 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:14:51,016 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:14:51,017 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-28 22:14:51,017 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-28 22:14:51,017 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-28 22:14:51,017 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-28 22:14:51,017 - __main__ - INFO - 自动清理管理器已初始化
2025-08-28 22:14:51,017 - __main__ - INFO - 所有组件初始化完成
2025-08-28 22:14:51,017 - __main__ - INFO - 启动Kafka数据过滤程序
2025-08-28 22:14:51,017 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-28 22:14:51,017 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:51,018 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:14:51,059 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:51,202 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:14:51,202 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:14:51,203 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-28 22:14:51,203 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-28 22:14:51,203 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x10270a160>}
2025-08-28 22:14:51,203 - __main__ - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-28 22:14:51,203 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-28 22:14:51,242 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-28 22:14:51,242 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-28 22:14:51,242 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-28 22:14:51,243 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-28 22:14:51,243 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:51,349 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:51,349 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:14:51,453 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-28 22:14:51,453 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:51,501 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:51,504 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-28 22:14:51,505 - kafka.coordinator.assignors.range - WARNING - No partition metadata for topic moyeT_dfycb_website
2025-08-28 22:14:51,560 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 1
2025-08-28 22:14:51,560 - kafka.consumer.subscription_state - INFO - Updated partition assignment: []
2025-08-28 22:14:51,560 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions set() for group moye-check-data
2025-08-28 22:14:51,560 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-28 22:14:51,560 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-28 22:14:51,596 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-28 22:14:51,634 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 2
2025-08-28 22:14:51,634 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-28 22:14:51,634 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-28 22:14:51,669 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:51,739 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:51,739 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:16:35,335 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-28 22:18:07,949 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-28 22:19:39,660 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-28 22:21:02,794 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 400
2025-08-28 22:22:39,998 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 500
2025-08-28 22:23:57,312 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 600
2025-08-28 22:25:27,739 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 700
2025-08-28 22:26:49,426 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 800
2025-08-28 22:28:56,118 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 900
2025-08-28 22:30:43,568 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1000
2025-08-28 22:32:12,429 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1100
2025-08-28 22:33:46,260 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1200
2025-08-28 22:36:07,371 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1300
2025-08-28 22:37:07,214 - __main__ - INFO - 收到信号 SIGINT，准备停止程序
2025-08-28 22:37:07,216 - __main__ - INFO - 正在停止应用程序...
2025-08-28 22:37:07,216 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-28 22:37:07,264 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 1354, 错误数: 0
2025-08-28 22:37:07,265 - __main__ - INFO - 开始清理资源
2025-08-28 22:37:07,265 - __main__ - INFO - 最终统计信息:
2025-08-28 22:37:07,265 - __main__ - INFO - 运行时间: 1336.25 秒
消费消息数: 1354
处理消息数: 1354
通过过滤数: 7
被过滤数: 1347
输出消息数: 7
错误数: 0
通过率: 0.52%
平均处理时间: 0.06 ms
当前吞吐量: 0.93 消息/秒
总体吞吐量: 1.01 消息/秒
2025-08-28 22:37:07,265 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-28 22:37:07,265 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-28 22:37:07,265 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-28 22:37:07,299 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-28 22:37:07,299 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-28 22:37:07,299 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-28 22:37:07,299 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-28 22:37:07,336 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-28 22:37:07,336 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-28 22:37:07,336 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:07,337 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:07,337 - kafka.consumer.fetcher - ERROR - Fetch to node 0 failed: Cancelled: <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>
2025-08-28 22:37:07,337 - kafka_data_filter.kafka_consumer - INFO - 消费者连接已关闭
2025-08-28 22:37:07,337 - __main__ - INFO - Kafka消费者已关闭
2025-08-28 22:37:07,337 - __main__ - INFO - 输出处理器已关闭
2025-08-28 22:37:07,337 - __main__ - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-28 22:37:07,337 - __main__ - INFO - 资源清理完成
2025-08-28 22:37:07,337 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-28 22:37:07,337 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-28 22:37:07,337 - kafka_data_filter.auto_cleanup_manager - INFO - 等待Kafka集群状态稳定...
2025-08-28 22:37:09,339 - kafka_data_filter.auto_cleanup_manager - INFO - 检查消费者组 moye-check-data 是否存在...
2025-08-28 22:37:09,340 - kafka.client - INFO - Closing idle connection 0, last active 1338469 ms ago
2025-08-28 22:37:09,340 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:09,341 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:37:09,389 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:37:09,436 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组存在性检查结果: exists=True, info=found_in_list
2025-08-28 22:37:09,436 - kafka_data_filter.auto_cleanup_manager - INFO - 获取消费者组详细信息进行二次确认...
2025-08-28 22:37:09,533 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:37:09,534 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:37:09,580 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:37:09,733 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:37:09,733 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:37:09,734 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:09,780 - kafka_data_filter.auto_cleanup_manager - INFO - 等待消费者组变为空状态...
2025-08-28 22:37:09,781 - kafka_data_filter.kafka_consumer_group_manager - INFO - 等待消费者组 moye-check-data 变为空状态...
2025-08-28 22:37:09,872 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:37:09,872 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:37:09,908 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:37:10,048 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:37:10,048 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:37:10,049 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:10,095 - kafka_data_filter.kafka_consumer_group_manager - INFO - 消费者组 moye-check-data 已变为空状态
2025-08-28 22:37:10,186 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:37:10,187 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:37:10,228 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:37:10,374 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:37:10,374 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:37:10,375 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:10,422 - kafka_data_filter.kafka_consumer_group_manager - INFO - 消费者组 moye-check-data 的信息已备份到: auto_cleanup_backup_moye-check-data_20250828_223710.json
2025-08-28 22:37:10,422 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组信息已备份到: auto_cleanup_backup_moye-check-data_20250828_223710.json
2025-08-28 22:37:10,422 - kafka_data_filter.auto_cleanup_manager - INFO - 删除消费者组: moye-check-data
2025-08-28 22:37:10,422 - kafka_data_filter.kafka_consumer_group_manager - INFO - 开始删除消费者组: moye-check-data
2025-08-28 22:37:10,513 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:37:10,513 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:37:10,556 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:37:10,699 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:37:10,699 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:37:10,700 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:10,746 - kafka_data_filter.kafka_consumer_group_manager - INFO - 正在删除消费者组: moye-check-data
2025-08-28 22:37:10,839 - kafka_data_filter.kafka_consumer_group_manager - WARNING - 删除结果检查不确定，尝试验证消费者组是否仍存在...
2025-08-28 22:37:12,981 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:37:12,981 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:37:13,022 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:37:13,177 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:37:13,178 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:37:13,178 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:13,226 - kafka_data_filter.kafka_consumer_group_manager - ERROR - 删除失败：消费者组 moye-check-data 仍然存在 (describe_result_unknown)
2025-08-28 22:37:13,226 - kafka_data_filter.auto_cleanup_manager - ERROR - ❌ 消费者组 moye-check-data 自动清理失败
2025-08-28 22:37:13,226 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:37:13,227 - kafka_data_filter.kafka_consumer_group_manager - INFO - Kafka管理客户端已关闭
2025-08-28 22:46:50,882 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-28 22:46:50,882 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-28 22:46:50,882 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-28 22:46:50,882 - __main__ - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-28 22:46:50,882 - kafka_data_filter.output_handler - INFO - 生成的输出文件路径: output/filtered_messages_20250828_224650.json
2025-08-28 22:46:50,882 - kafka_data_filter.output_handler - INFO - 创建新的JSON数组文件: output/filtered_messages_20250828_224650.json
2025-08-28 22:46:50,882 - __main__ - INFO - 输出处理器已初始化
2025-08-28 22:46:50,882 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-28 22:46:50,882 - __main__ - INFO - 统计模块已初始化
2025-08-28 22:46:50,882 - __main__ - INFO - Kafka消费者已初始化
2025-08-28 22:46:50,887 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:46:50,887 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:46:50,888 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:46:51,039 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:46:51,039 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:46:51,039 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:46:51,189 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:46:51,190 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:46:51,280 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:46:51,281 - kafka.conn - INFO - Probing node 0 broker version
2025-08-28 22:46:51,283 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:46:51,283 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:46:51,422 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:46:51,422 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:46:51,422 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-28 22:46:51,422 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-28 22:46:51,422 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-28 22:46:51,422 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-28 22:46:51,422 - __main__ - INFO - 自动清理管理器已初始化
2025-08-28 22:46:51,422 - __main__ - INFO - 所有组件初始化完成
2025-08-28 22:46:51,422 - __main__ - INFO - 启动Kafka数据过滤程序
2025-08-28 22:46:51,422 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-28 22:46:51,422 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:46:51,423 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:46:51,425 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:46:51,571 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:46:51,571 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:46:51,572 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-28 22:46:51,572 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-28 22:46:51,572 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x101c0e480>}
2025-08-28 22:46:51,572 - __main__ - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-28 22:46:51,572 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-28 22:46:51,612 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-28 22:46:51,612 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-28 22:46:51,612 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-28 22:46:51,613 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-28 22:46:51,613 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:46:51,719 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:46:51,719 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:46:51,824 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-28 22:46:51,867 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-28 22:46:51,867 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:46:51,870 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:46:51,908 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 1
2025-08-28 22:46:51,908 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-28 22:46:51,908 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-28 22:48:24,955 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-28 22:49:44,071 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-28 22:51:21,480 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-28 22:54:11,550 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 400
2025-08-28 22:55:58,728 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 500
2025-08-28 22:58:09,863 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 600
2025-08-28 22:58:39,807 - __main__ - INFO - 收到信号 SIGINT，准备停止程序
2025-08-28 22:58:39,808 - __main__ - INFO - 正在停止应用程序...
2025-08-28 22:58:39,808 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-28 22:58:40,338 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 625, 错误数: 0
2025-08-28 22:58:40,338 - __main__ - INFO - 开始清理资源
2025-08-28 22:58:40,338 - __main__ - INFO - 最终统计信息:
2025-08-28 22:58:40,339 - __main__ - INFO - 运行时间: 708.92 秒
消费消息数: 625
处理消息数: 625
通过过滤数: 1
被过滤数: 624
输出消息数: 1
错误数: 0
通过率: 0.16%
平均处理时间: 0.12 ms
当前吞吐量: 0.96 消息/秒
总体吞吐量: 0.88 消息/秒
2025-08-28 22:58:40,339 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-28 22:58:40,339 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-28 22:58:40,339 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-28 22:58:40,378 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-28 22:58:40,379 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-28 22:58:40,379 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-28 22:58:40,379 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-28 22:58:40,421 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-28 22:58:40,422 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-28 22:58:40,422 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:58:40,422 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:58:40,422 - kafka_data_filter.kafka_consumer - INFO - 消费者连接已关闭
2025-08-28 22:58:40,422 - __main__ - INFO - Kafka消费者已关闭
2025-08-28 22:58:40,422 - __main__ - INFO - 输出处理器已关闭
2025-08-28 22:58:40,422 - __main__ - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-28 22:58:40,423 - __main__ - INFO - 资源清理完成
2025-08-28 22:58:40,423 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-28 22:58:40,423 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-28 22:58:40,423 - kafka_data_filter.auto_cleanup_manager - INFO - 等待Kafka集群状态稳定...
2025-08-28 22:58:42,427 - kafka_data_filter.auto_cleanup_manager - INFO - 检查消费者组 moye-check-data 是否存在...
2025-08-28 22:58:42,428 - kafka.conn - ERROR - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: socket disconnected
2025-08-28 22:58:42,428 - kafka.conn - ERROR - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. KafkaConnectionError: socket disconnected
2025-08-28 22:58:42,428 - kafka.client - WARNING - Node 0 connection failed -- refreshing metadata
2025-08-28 22:58:42,429 - kafka_data_filter.kafka_consumer_group_manager - ERROR - 检查消费者组存在性失败: Invalid file descriptor: -1
2025-08-28 22:58:42,429 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组存在性检查结果: exists=False, info=error_Invalid file descriptor: -1
2025-08-28 22:58:42,429 - kafka_data_filter.auto_cleanup_manager - INFO - 轻量级检查失败，尝试备用检查方法...
2025-08-28 22:58:42,429 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:58:42,432 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:58:42,561 - kafka_data_filter.auto_cleanup_manager - INFO - 备用检查结果: exists=True, info=fallback_exists_Empty
2025-08-28 22:58:42,561 - kafka_data_filter.auto_cleanup_manager - INFO - 获取消费者组详细信息进行二次确认...
2025-08-28 22:58:42,651 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:58:42,652 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:58:42,653 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:58:42,797 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:58:42,797 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:58:42,797 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:58:42,838 - kafka_data_filter.auto_cleanup_manager - INFO - 等待消费者组变为空状态...
2025-08-28 22:58:42,838 - kafka_data_filter.kafka_consumer_group_manager - INFO - 等待消费者组 moye-check-data 变为空状态...
2025-08-28 22:58:42,916 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:58:42,916 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:58:42,917 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:58:43,063 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:58:43,063 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:58:43,064 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:58:43,104 - kafka_data_filter.kafka_consumer_group_manager - INFO - 消费者组 moye-check-data 已变为空状态
2025-08-28 22:58:43,182 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:58:43,183 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:58:43,184 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:58:43,324 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:58:43,324 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:58:43,324 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:58:43,367 - kafka_data_filter.kafka_consumer_group_manager - INFO - 消费者组 moye-check-data 的信息已备份到: auto_cleanup_backup_moye-check-data_20250828_225843.json
2025-08-28 22:58:43,367 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组信息已备份到: auto_cleanup_backup_moye-check-data_20250828_225843.json
2025-08-28 22:58:43,367 - kafka_data_filter.auto_cleanup_manager - INFO - 删除消费者组: moye-check-data
2025-08-28 22:58:43,368 - kafka_data_filter.kafka_consumer_group_manager - INFO - 开始删除消费者组: moye-check-data
2025-08-28 22:58:43,446 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:58:43,446 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:58:43,449 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:58:43,595 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:58:43,595 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:58:43,595 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:58:43,636 - kafka_data_filter.kafka_consumer_group_manager - INFO - 正在删除消费者组: moye-check-data
2025-08-28 22:58:43,714 - kafka_data_filter.kafka_consumer_group_manager - WARNING - 删除结果检查不确定，尝试验证消费者组是否仍存在...
2025-08-28 22:58:45,834 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:58:45,834 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:58:45,837 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:58:45,978 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:58:45,979 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:58:45,979 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:58:46,018 - kafka_data_filter.kafka_consumer_group_manager - ERROR - 删除失败：消费者组 moye-check-data 仍然存在 (describe_result_unknown)
2025-08-28 22:58:46,018 - kafka_data_filter.auto_cleanup_manager - ERROR - ❌ 消费者组 moye-check-data 自动清理失败
2025-08-28 22:58:46,018 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:58:46,019 - kafka_data_filter.kafka_consumer_group_manager - INFO - Kafka管理客户端已关闭
