#!/usr/bin/env python3
"""
增强的Kafka消费者

支持强制从最新位置开始消费，无论消费者组是否已存在
"""

import json
import logging
import time
from typing import Callable, Optional, Dict, Any
from kafka import KafkaConsumer
from kafka.structs import TopicPartition


class EnhancedKafkaMessageConsumer:
    """增强的Kafka消息消费者类"""
    
    def __init__(self, config: Dict[str, Any], force_latest: bool = False):
        """
        初始化增强的Kafka消费者
        
        参数:
            config: Kafka消费者配置字典
            force_latest: 是否强制从最新位置开始消费
        """
        self.config = config
        self.force_latest = force_latest
        self.consumer = None
        self.topic = None
        self.logger = logging.getLogger(__name__)
        self.is_running = False
        self.message_count = 0
        self.error_count = 0
        
    def connect(self, topic: str) -> None:
        """
        连接到Kafka集群并订阅指定topic
        
        参数:
            topic: 要订阅的topic名称
        """
        try:
            self.topic = topic
            
            # 创建Kafka消费者
            self.consumer = KafkaConsumer(
                topic,
                **self.config
            )
            
            self.logger.info(f"成功连接到Kafka集群，订阅topic: {topic}")
            self.logger.info(f"消费者配置: {self._get_safe_config()}")
            
            # 如果启用了force_latest，强制seek到最新位置
            if self.force_latest:
                self._seek_to_latest()
            
        except Exception as e:
            self.logger.error(f"连接Kafka失败: {e}")
            raise RuntimeError(f"Kafka连接失败: {e}")
    
    def _seek_to_latest(self) -> None:
        """强制将消费者位置设置到最新"""
        try:
            self.logger.info("强制设置消费位置到最新...")
            
            # 获取分配的分区
            partitions = self.consumer.assignment()
            
            if not partitions:
                # 如果还没有分配分区，先进行一次poll来触发分区分配
                self.logger.info("触发分区分配...")
                self.consumer.poll(timeout_ms=1000, max_records=0)
                partitions = self.consumer.assignment()
            
            if partitions:
                # 获取每个分区的最新offset
                end_offsets = self.consumer.end_offsets(partitions)
                
                # 将每个分区的消费位置设置到最新
                for partition in partitions:
                    latest_offset = end_offsets.get(partition, 0)
                    self.consumer.seek(partition, latest_offset)
                    self.logger.info(f"分区 {partition} 位置设置到: {latest_offset}")
                
                self.logger.info("✅ 已强制设置到最新消费位置")
            else:
                self.logger.warning("⚠️ 无法获取分区分配，跳过强制设置")
                
        except Exception as e:
            self.logger.error(f"强制设置到最新位置失败: {e}")
            # 不抛出异常，允许程序继续运行
    
    def _get_safe_config(self) -> Dict[str, Any]:
        """
        获取安全的配置信息（隐藏敏感信息）
        
        返回:
            安全的配置字典
        """
        safe_config = self.config.copy()
        
        # 隐藏敏感信息
        sensitive_keys = ['sasl_password', 'ssl_password']
        for key in sensitive_keys:
            if key in safe_config:
                safe_config[key] = '***'
        
        return safe_config
    
    def consume_messages(self, 
                        message_handler: Callable[[dict], None],
                        max_messages: Optional[int] = None,
                        timeout_ms: int = 1000) -> None:
        """
        消费消息并调用处理函数
        
        参数:
            message_handler: 消息处理函数，接收解析后的JSON数据
            max_messages: 最大消费消息数量，None表示无限制
            timeout_ms: 消费超时时间（毫秒）
        """
        if not self.consumer:
            raise RuntimeError("消费者未连接，请先调用connect方法")
        
        self.is_running = True
        self.message_count = 0
        self.error_count = 0
        
        max_msg_info = max_messages or '无限制'
        force_info = '（强制从最新位置开始）' if self.force_latest else ''
        self.logger.info(f"开始消费消息，最大消息数: {max_msg_info}{force_info}")
        
        try:
            while self.is_running:
                # 检查是否达到最大消息数
                if max_messages and self.message_count >= max_messages:
                    self.logger.info(f"已达到最大消息数限制: {max_messages}")
                    break
                
                # 拉取消息
                message_batch = self.consumer.poll(timeout_ms=timeout_ms)
                
                if not message_batch:
                    continue
                
                # 处理消息批次
                for topic_partition, messages in message_batch.items():
                    for message in messages:
                        try:
                            # 解析JSON消息
                            json_data = self._parse_message(message)
                            
                            if json_data is not None:
                                # 调用消息处理函数
                                message_handler(json_data)
                                self.message_count += 1
                                
                                # 记录处理进度
                                if self.message_count % 100 == 0:
                                    self.logger.info(f"已处理消息数: {self.message_count}")
                            
                        except Exception as e:
                            self.error_count += 1
                            self.logger.error(f"处理消息失败: {e}")
                            
                            # 如果错误太多，停止消费
                            if self.error_count > 100:
                                self.logger.error("错误数量过多，停止消费")
                                self.stop()
                                break
                
                # 检查是否需要停止
                if not self.is_running:
                    break
                    
        except Exception as e:
            self.logger.error(f"消费消息时发生错误: {e}")
            raise
        finally:
            self.logger.info(f"消费结束，共处理 {self.message_count} 条消息，错误 {self.error_count} 次")
    
    def _parse_message(self, message) -> Optional[dict]:
        """
        解析Kafka消息为JSON数据
        
        参数:
            message: Kafka消息对象
            
        返回:
            解析后的JSON数据，解析失败返回None
        """
        try:
            # 获取消息值
            message_value = message.value
            
            if message_value is None:
                self.logger.debug("收到空消息")
                return None
            
            # 如果是字节类型，先解码
            if isinstance(message_value, bytes):
                message_value = message_value.decode('utf-8')
            
            # 解析JSON
            json_data = json.loads(message_value)
            
            # 添加消息元数据
            json_data['_kafka_metadata'] = {
                'topic': message.topic,
                'partition': message.partition,
                'offset': message.offset,
                'timestamp': message.timestamp,
                'key': message.key.decode('utf-8') if message.key else None
            }
            
            return json_data
            
        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON解析失败: {e}, 消息内容: {message_value}")
            return None
        except UnicodeDecodeError as e:
            self.logger.warning(f"消息解码失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"解析消息时发生未知错误: {e}")
            return None
    
    def stop(self) -> None:
        """停止消费消息"""
        self.is_running = False
        self.logger.info("停止消费消息")
    
    def graceful_shutdown(self, timeout_seconds: int = 30) -> bool:
        """
        优雅关闭消费者
        
        参数:
            timeout_seconds: 关闭超时时间（秒）
            
        返回:
            是否成功关闭
        """
        try:
            self.logger.info("开始优雅关闭增强Kafka消费者...")
            
            # 停止消费循环
            self.stop()
            
            if self.consumer:
                # 提交当前offset
                try:
                    self.logger.info("提交当前offset...")
                    self.consumer.commit()
                    self.logger.info("Offset提交成功")
                except Exception as e:
                    self.logger.warning(f"提交offset失败: {e}")
                
                # 取消订阅
                try:
                    self.logger.info("取消topic订阅...")
                    self.consumer.unsubscribe()
                    self.logger.info("取消订阅成功")
                except Exception as e:
                    self.logger.warning(f"取消订阅失败: {e}")
                
                # 关闭消费者
                try:
                    self.logger.info("关闭消费者连接...")
                    self.consumer.close()
                    self.logger.info("消费者连接已关闭")
                    return True
                except Exception as e:
                    self.logger.error(f"关闭消费者失败: {e}")
                    return False
                finally:
                    self.consumer = None
            
            return True
            
        except Exception as e:
            self.logger.error(f"优雅关闭失败: {e}")
            return False
    
    def close(self) -> None:
        """关闭消费者连接"""
        if self.consumer:
            try:
                self.consumer.close()
                self.logger.info("增强Kafka消费者连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭增强Kafka消费者失败: {e}")
            finally:
                self.consumer = None
    
    def commit_offsets(self) -> bool:
        """
        手动提交当前offset
        
        返回:
            是否提交成功
        """
        if not self.consumer:
            self.logger.warning("消费者未连接，无法提交offset")
            return False
        
        try:
            self.consumer.commit()
            self.logger.info("手动提交offset成功")
            return True
        except Exception as e:
            self.logger.error(f"手动提交offset失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取消费统计信息
        
        返回:
            统计信息字典
        """
        return {
            'message_count': self.message_count,
            'error_count': self.error_count,
            'is_running': self.is_running,
            'topic': self.topic,
            'force_latest': self.force_latest
        }
