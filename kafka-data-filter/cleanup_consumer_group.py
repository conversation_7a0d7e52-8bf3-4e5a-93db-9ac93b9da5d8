#!/usr/bin/env python3
"""
独立的Kafka消费者组清理脚本

用于在程序外部清理指定的消费者组
支持批量清理和安全检查
"""

import sys
import os
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.auto_cleanup_manager import AutoCleanupManager


class IndependentCleanupTool:
    """独立清理工具"""
    
    def __init__(self, config_path: str = None, bootstrap_servers: str = None):
        """
        初始化清理工具
        
        参数:
            config_path: 配置文件路径
            bootstrap_servers: Kafka服务器地址（直接指定）
        """
        self.logger = self._setup_logging()
        
        if config_path:
            # 从配置文件加载
            self.config_manager = ConfigManager(config_path)
            self.kafka_config = self.config_manager.get_kafka_config()
        elif bootstrap_servers:
            # 直接使用指定的服务器地址
            self.kafka_config = {
                'bootstrap_servers': bootstrap_servers,
                'group_id': 'default-cleanup-group'  # 默认组ID
            }
        else:
            raise ValueError("必须提供配置文件路径或Kafka服务器地址")
        
        self.logger.info(f"连接到Kafka集群: {self.kafka_config['bootstrap_servers']}")
    
    def _setup_logging(self) -> logging.Logger:
        """配置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('cleanup_consumer_group.log', encoding='utf-8')
            ]
        )
        return logging.getLogger(__name__)
    
    def cleanup_single_group(self, group_id: str, force: bool = False, 
                           backup: bool = True) -> bool:
        """
        清理单个消费者组
        
        参数:
            group_id: 消费者组ID
            force: 是否强制清理
            backup: 是否备份
            
        返回:
            是否清理成功
        """
        try:
            self.logger.info(f"开始清理消费者组: {group_id}")
            
            # 创建临时的自动清理管理器
            temp_kafka_config = self.kafka_config.copy()
            temp_kafka_config['group_id'] = group_id
            
            cleanup_manager = AutoCleanupManager(
                kafka_config=temp_kafka_config,
                cleanup_on_exit=False,  # 禁用自动清理，手动控制
                backup_before_cleanup=backup
            )
            
            try:
                # 执行手动清理
                success = cleanup_manager.manual_cleanup(force=force)
                
                if success:
                    self.logger.info(f"✅ 消费者组 {group_id} 清理成功")
                else:
                    self.logger.error(f"❌ 消费者组 {group_id} 清理失败")
                
                return success
                
            finally:
                cleanup_manager.close()
                
        except Exception as e:
            self.logger.error(f"清理消费者组 {group_id} 时发生错误: {e}")
            return False
    
    def cleanup_multiple_groups(self, group_ids: List[str], force: bool = False,
                              backup: bool = True, continue_on_error: bool = True) -> Dict[str, bool]:
        """
        批量清理多个消费者组
        
        参数:
            group_ids: 消费者组ID列表
            force: 是否强制清理
            backup: 是否备份
            continue_on_error: 遇到错误是否继续
            
        返回:
            清理结果字典 {group_id: success}
        """
        results = {}
        
        self.logger.info(f"开始批量清理 {len(group_ids)} 个消费者组")
        
        for i, group_id in enumerate(group_ids, 1):
            self.logger.info(f"处理第 {i}/{len(group_ids)} 个消费者组: {group_id}")
            
            try:
                success = self.cleanup_single_group(group_id, force=force, backup=backup)
                results[group_id] = success
                
                if not success and not continue_on_error:
                    self.logger.error(f"消费者组 {group_id} 清理失败，停止批量处理")
                    break
                    
            except Exception as e:
                self.logger.error(f"处理消费者组 {group_id} 时发生异常: {e}")
                results[group_id] = False
                
                if not continue_on_error:
                    self.logger.error("遇到异常，停止批量处理")
                    break
        
        # 输出汇总结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        self.logger.info(f"批量清理完成: {success_count}/{total_count} 个消费者组清理成功")
        
        if success_count < total_count:
            self.logger.warning("部分消费者组清理失败:")
            for group_id, success in results.items():
                if not success:
                    self.logger.warning(f"  - {group_id}: 失败")
        
        return results
    
    def cleanup_from_config(self, force: bool = False, backup: bool = True) -> bool:
        """
        从配置文件中清理消费者组
        
        参数:
            force: 是否强制清理
            backup: 是否备份
            
        返回:
            是否清理成功
        """
        if not hasattr(self, 'config_manager'):
            self.logger.error("未提供配置文件，无法执行此操作")
            return False
        
        group_id = self.kafka_config.get('group_id')
        if not group_id:
            self.logger.error("配置文件中未找到消费者组ID")
            return False
        
        return self.cleanup_single_group(group_id, force=force, backup=backup)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Kafka消费者组清理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 从配置文件清理消费者组
  python cleanup_consumer_group.py --config filter_config.yaml
  
  # 清理指定的消费者组
  python cleanup_consumer_group.py --bootstrap-servers localhost:9092 --group moye-check-data
  
  # 批量清理多个消费者组
  python cleanup_consumer_group.py --config filter_config.yaml --groups group1,group2,group3
  
  # 强制清理（跳过安全检查）
  python cleanup_consumer_group.py --config filter_config.yaml --force
  
  # 清理时跳过备份
  python cleanup_consumer_group.py --config filter_config.yaml --no-backup
        """
    )
    
    # 连接配置参数
    connection_group = parser.add_mutually_exclusive_group(required=True)
    connection_group.add_argument(
        "--config", "-c",
        help="配置文件路径"
    )
    connection_group.add_argument(
        "--bootstrap-servers", "-b",
        help="Kafka集群地址 (例如: localhost:9092)"
    )
    
    # 清理目标参数
    target_group = parser.add_mutually_exclusive_group()
    target_group.add_argument(
        "--group", "-g",
        help="要清理的消费者组ID（与--bootstrap-servers一起使用）"
    )
    target_group.add_argument(
        "--groups",
        help="要清理的消费者组ID列表，用逗号分隔"
    )
    
    # 清理选项
    parser.add_argument(
        "--force",
        action="store_true",
        help="强制清理（跳过安全检查）"
    )
    
    parser.add_argument(
        "--no-backup",
        action="store_true",
        help="跳过备份"
    )
    
    parser.add_argument(
        "--continue-on-error",
        action="store_true",
        default=True,
        help="批量清理时遇到错误继续处理（默认启用）"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="试运行模式，只检查不执行清理"
    )
    
    args = parser.parse_args()
    
    # 验证参数
    if args.bootstrap_servers and not args.group and not args.groups:
        print("错误: 使用 --bootstrap-servers 时必须指定 --group 或 --groups")
        sys.exit(1)
    
    # 创建清理工具
    try:
        if args.config:
            if not os.path.exists(args.config):
                print(f"错误: 配置文件不存在: {args.config}")
                sys.exit(1)
            cleanup_tool = IndependentCleanupTool(config_path=args.config)
        else:
            cleanup_tool = IndependentCleanupTool(bootstrap_servers=args.bootstrap_servers)
    except Exception as e:
        print(f"初始化清理工具失败: {e}")
        sys.exit(1)
    
    try:
        if args.dry_run:
            print("🔍 试运行模式 - 只检查不执行实际清理")
        
        # 确定要清理的消费者组
        if args.groups:
            # 批量清理
            group_ids = [g.strip() for g in args.groups.split(',') if g.strip()]
            print(f"准备清理 {len(group_ids)} 个消费者组: {', '.join(group_ids)}")
            
            if not args.dry_run:
                # 确认操作
                if not args.force:
                    confirm = input("确定要批量清理这些消费者组吗？(y/N): ")
                    if confirm.lower() not in ['y', 'yes']:
                        print("操作已取消")
                        sys.exit(0)
                
                results = cleanup_tool.cleanup_multiple_groups(
                    group_ids=group_ids,
                    force=args.force,
                    backup=not args.no_backup,
                    continue_on_error=args.continue_on_error
                )
                
                # 检查结果
                failed_groups = [gid for gid, success in results.items() if not success]
                if failed_groups:
                    print(f"\n❌ 以下消费者组清理失败: {', '.join(failed_groups)}")
                    sys.exit(1)
                else:
                    print(f"\n✅ 所有 {len(group_ids)} 个消费者组清理成功")
        
        elif args.group:
            # 单个清理
            print(f"准备清理消费者组: {args.group}")
            
            if not args.dry_run:
                # 确认操作
                if not args.force:
                    confirm = input(f"确定要清理消费者组 '{args.group}' 吗？(y/N): ")
                    if confirm.lower() not in ['y', 'yes']:
                        print("操作已取消")
                        sys.exit(0)
                
                success = cleanup_tool.cleanup_single_group(
                    group_id=args.group,
                    force=args.force,
                    backup=not args.no_backup
                )
                
                if success:
                    print(f"\n✅ 消费者组 {args.group} 清理成功")
                else:
                    print(f"\n❌ 消费者组 {args.group} 清理失败")
                    sys.exit(1)
        
        else:
            # 从配置文件清理
            if not args.config:
                print("错误: 必须提供配置文件或指定消费者组")
                sys.exit(1)
            
            group_id = cleanup_tool.kafka_config.get('group_id', 'unknown')
            print(f"准备清理配置文件中的消费者组: {group_id}")
            
            if not args.dry_run:
                # 确认操作
                if not args.force:
                    confirm = input(f"确定要清理消费者组 '{group_id}' 吗？(y/N): ")
                    if confirm.lower() not in ['y', 'yes']:
                        print("操作已取消")
                        sys.exit(0)
                
                success = cleanup_tool.cleanup_from_config(
                    force=args.force,
                    backup=not args.no_backup
                )
                
                if success:
                    print(f"\n✅ 消费者组 {group_id} 清理成功")
                else:
                    print(f"\n❌ 消费者组 {group_id} 清理失败")
                    sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"执行清理操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
