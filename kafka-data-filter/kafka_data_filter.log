2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-22 17:01:32,641 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-22 17:01:32,641 - kafka_data_filter.output_handler - INFO - 生成的输出文件路径: output/filtered_messages_20250822_170132.json
2025-08-22 17:01:32,641 - kafka_data_filter.output_handler - INFO - 创建新的JSON数组文件: output/filtered_messages_20250822_170132.json
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-22 17:01:32,641 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-22 17:01:32,641 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-22 17:01:32,646 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:32,646 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:01:32,724 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:01:32,889 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:01:32,889 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:01:32,890 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:01:33,066 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:01:33,067 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:01:33,194 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:33,195 - kafka.conn - INFO - Probing node 0 broker version
2025-08-22 17:01:33,260 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:01:33,260 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:01:33,428 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:01:33,428 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:01:33,428 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-22 17:01:33,428 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-22 17:01:33,428 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-22 17:01:33,428 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-22 17:01:33,429 - kafka_data_filter.main - INFO - 自动清理管理器已初始化
2025-08-22 17:01:33,429 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-22 17:01:33,429 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-22 17:01:33,429 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-22 17:01:33,429 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:33,429 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:01:33,500 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:01:33,690 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:01:33,690 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:01:33,691 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-22 17:01:33,692 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-22 17:01:33,692 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x102775d00>}
2025-08-22 17:01:33,692 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-22 17:01:33,692 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-22 17:01:33,774 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-22 17:01:33,774 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-22 17:01:33,775 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-22 17:01:33,775 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-22 17:01:33,776 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:33,881 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:01:33,882 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:01:33,988 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-22 17:01:34,055 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-22 17:01:34,055 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:01:34,130 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 1
2025-08-22 17:01:34,130 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-22 17:01:34,130 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-22 17:01:34,130 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:02:28,678 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-22 17:03:02,758 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-22 17:03:50,392 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-22 17:04:31,345 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 400
2025-08-22 17:05:23,963 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 500
2025-08-22 17:06:10,368 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 600
2025-08-22 17:06:22,148 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-22 17:06:22,148 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-22 17:06:22,148 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 17:06:22,655 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 631, 错误数: 0
2025-08-22 17:06:22,656 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-22 17:06:22,656 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-22 17:06:22,656 - kafka_data_filter.main - INFO - 运行时间: 289.23 秒
消费消息数: 631
处理消息数: 631
通过过滤数: 3
被过滤数: 628
输出消息数: 3
错误数: 0
通过率: 0.48%
平均处理时间: 0.04 ms
当前吞吐量: 2.44 消息/秒
总体吞吐量: 2.18 消息/秒
2025-08-22 17:06:22,656 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-22 17:06:22,657 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 17:06:22,657 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-22 17:06:22,730 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-22 17:06:22,731 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-22 17:06:22,731 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-22 17:06:22,731 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-22 17:06:22,795 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-22 17:06:22,795 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-22 17:06:22,796 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:22,796 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:22,797 - kafka_data_filter.kafka_consumer - INFO - 消费者连接已关闭
2025-08-22 17:06:22,797 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-22 17:06:22,797 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-22 17:06:22,797 - kafka_data_filter.main - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-22 17:06:22,797 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-22 17:06:22,797 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-22 17:06:22,797 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-22 17:06:22,797 - kafka_data_filter.auto_cleanup_manager - INFO - 等待Kafka集群状态稳定...
2025-08-22 17:06:24,801 - kafka_data_filter.auto_cleanup_manager - INFO - 检查消费者组 moye-check-data 是否存在...
2025-08-22 17:06:24,867 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组存在性检查结果: exists=True, info=found_in_list
2025-08-22 17:06:24,867 - kafka_data_filter.auto_cleanup_manager - INFO - 获取消费者组详细信息进行二次确认...
2025-08-22 17:06:24,998 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:24,998 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:25,074 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:25,248 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:25,249 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:25,249 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:25,318 - kafka_data_filter.auto_cleanup_manager - INFO - 等待消费者组变为空状态...
2025-08-22 17:06:25,318 - kafka_data_filter.kafka_consumer_group_manager - INFO - 等待消费者组 moye-check-data 变为空状态...
2025-08-22 17:06:25,472 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:25,473 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:25,534 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:25,703 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:25,704 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:25,704 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:25,791 - kafka_data_filter.kafka_consumer_group_manager - INFO - 消费者组 moye-check-data 已变为空状态
2025-08-22 17:06:25,925 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:25,926 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:25,994 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:26,158 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:26,158 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:26,159 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:26,228 - kafka_data_filter.kafka_consumer_group_manager - INFO - 消费者组 moye-check-data 的信息已备份到: auto_cleanup_backup_moye-check-data_20250822_170625.json
2025-08-22 17:06:26,229 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组信息已备份到: auto_cleanup_backup_moye-check-data_20250822_170625.json
2025-08-22 17:06:26,229 - kafka_data_filter.auto_cleanup_manager - INFO - 删除消费者组: moye-check-data
2025-08-22 17:06:26,229 - kafka_data_filter.kafka_consumer_group_manager - INFO - 开始删除消费者组: moye-check-data
2025-08-22 17:06:26,415 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:26,415 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:26,487 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:26,663 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:26,663 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:26,664 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:26,732 - kafka_data_filter.kafka_consumer_group_manager - INFO - 正在删除消费者组: moye-check-data
2025-08-22 17:06:26,867 - kafka_data_filter.kafka_consumer_group_manager - WARNING - 删除结果检查不确定，尝试验证消费者组是否仍存在...
2025-08-22 17:06:29,107 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 17:06:29,108 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 17:06:29,172 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 17:06:29,343 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 17:06:29,343 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 17:06:29,344 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:29,409 - kafka_data_filter.kafka_consumer_group_manager - ERROR - 删除失败：消费者组 moye-check-data 仍然存在 (describe_result_unknown)
2025-08-22 17:06:29,409 - kafka_data_filter.auto_cleanup_manager - ERROR - ❌ 消费者组 moye-check-data 自动清理失败
2025-08-22 17:06:29,409 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 17:06:29,409 - kafka_data_filter.kafka_consumer_group_manager - INFO - Kafka管理客户端已关闭
2025-08-28 22:14:50,380 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-28 22:14:50,381 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-28 22:14:50,381 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-28 22:14:50,381 - __main__ - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-28 22:14:50,381 - kafka_data_filter.output_handler - INFO - 生成的输出文件路径: output/filtered_messages_20250828_221450.json
2025-08-28 22:14:50,381 - kafka_data_filter.output_handler - INFO - 创建新的JSON数组文件: output/filtered_messages_20250828_221450.json
2025-08-28 22:14:50,381 - __main__ - INFO - 输出处理器已初始化
2025-08-28 22:14:50,381 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-28 22:14:50,381 - __main__ - INFO - 统计模块已初始化
2025-08-28 22:14:50,381 - __main__ - INFO - Kafka消费者已初始化
2025-08-28 22:14:50,386 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:50,386 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:14:50,434 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:50,586 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:14:50,586 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:14:50,586 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:14:50,737 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:14:50,737 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:14:50,828 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:50,828 - kafka.conn - INFO - Probing node 0 broker version
2025-08-28 22:14:50,870 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:50,870 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:14:51,016 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:14:51,016 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:14:51,017 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-28 22:14:51,017 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-28 22:14:51,017 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-28 22:14:51,017 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-28 22:14:51,017 - __main__ - INFO - 自动清理管理器已初始化
2025-08-28 22:14:51,017 - __main__ - INFO - 所有组件初始化完成
2025-08-28 22:14:51,017 - __main__ - INFO - 启动Kafka数据过滤程序
2025-08-28 22:14:51,017 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-28 22:14:51,017 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:51,018 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-28 22:14:51,059 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:51,202 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-28 22:14:51,202 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-28 22:14:51,203 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-28 22:14:51,203 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-28 22:14:51,203 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x10270a160>}
2025-08-28 22:14:51,203 - __main__ - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-28 22:14:51,203 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-28 22:14:51,242 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-28 22:14:51,242 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-28 22:14:51,242 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-28 22:14:51,243 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-28 22:14:51,243 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:51,349 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:51,349 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:14:51,453 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-28 22:14:51,453 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:51,501 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:51,504 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-28 22:14:51,505 - kafka.coordinator.assignors.range - WARNING - No partition metadata for topic moyeT_dfycb_website
2025-08-28 22:14:51,560 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 1
2025-08-28 22:14:51,560 - kafka.consumer.subscription_state - INFO - Updated partition assignment: []
2025-08-28 22:14:51,560 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions set() for group moye-check-data
2025-08-28 22:14:51,560 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-28 22:14:51,560 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-28 22:14:51,596 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-28 22:14:51,634 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 2
2025-08-28 22:14:51,634 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-28 22:14:51,634 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-28 22:14:51,669 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-28 22:14:51,739 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-28 22:14:51,739 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-28 22:16:35,335 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-28 22:18:07,949 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-28 22:19:39,660 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-28 22:21:02,794 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 400
2025-08-28 22:22:39,998 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 500
