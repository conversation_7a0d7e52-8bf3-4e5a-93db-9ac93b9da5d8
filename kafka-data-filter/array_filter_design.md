# 数组过滤语法设计文档

## 问题分析

当前的过滤器在处理数组字段时存在以下问题：

1. **路径访问问题**: `n_content_spam.class2` 返回 `None`，因为 `n_content_spam` 是数组，不能直接访问 `.class2`
2. **语义不明确**: 没有明确指定是要匹配数组中的任意元素还是所有元素
3. **比较逻辑缺陷**: 当字段值为 `None` 时，比较结果不可预测

## 新语法设计

### 1. 数组访问模式

支持两种数组访问模式：

- `field[ANY].subfield` - 数组中任意元素满足条件
- `field[ALL].subfield` - 数组中所有元素都满足条件
- `field.subfield` - 向后兼容，默认为 ANY 模式

### 2. 语法示例

```
# ANY模式 - 数组中任意元素的class2不在排除列表中
n_content_spam[ANY].class2 NOT IN ("广告信息", "股票资讯", "新闻合集")

# ALL模式 - 数组中所有元素的class2都不在排除列表中  
n_content_spam[ALL].class2 NOT IN ("广告信息", "股票资讯", "新闻合集")

# 向后兼容 - 等同于ANY模式
n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集")

# 数值比较
n_content_spam[ANY].probability > 0.8
n_content_spam[ALL].probability > 0.5

# 等值比较
n_content_spam[ANY].id = 10
```

### 3. 语义定义

#### ANY模式语义
- **等值比较**: `field[ANY].sub = value` → 数组中至少有一个元素的sub字段等于value
- **IN比较**: `field[ANY].sub IN (list)` → 数组中至少有一个元素的sub字段在list中
- **NOT IN比较**: `field[ANY].sub NOT IN (list)` → 数组中至少有一个元素的sub字段不在list中
- **数值比较**: `field[ANY].sub > value` → 数组中至少有一个元素的sub字段大于value

#### ALL模式语义
- **等值比较**: `field[ALL].sub = value` → 数组中所有元素的sub字段都等于value
- **IN比较**: `field[ALL].sub IN (list)` → 数组中所有元素的sub字段都在list中
- **NOT IN比较**: `field[ALL].sub NOT IN (list)` → 数组中所有元素的sub字段都不在list中
- **数值比较**: `field[ALL].sub > value` → 数组中所有元素的sub字段都大于value

### 4. 特殊情况处理

- **空数组**: 
  - ANY模式返回 `False`（没有元素满足条件）
  - ALL模式返回 `True`（所有元素都满足条件，空集合的全称量化为真）
  
- **字段不存在**: 
  - 如果数组元素中某个字段不存在，该元素被视为不满足条件
  
- **类型不匹配**: 
  - 如果字段值类型与比较值类型不匹配，该元素被视为不满足条件

## 实现计划

### 1. 扩展词法分析器
- 添加 `[ANY]` 和 `[ALL]` token类型
- 修改tokenize方法识别这些标记

### 2. 修改DataAccessor
- 扩展 `get_field_value` 方法
- 支持数组访问模式解析
- 返回特殊的数组访问对象

### 3. 修改FilterParser
- 扩展比较逻辑处理数组值
- 实现ANY/ALL模式的比较算法

### 4. 向后兼容
- 保持现有语法 `field.subfield` 的支持
- 默认行为设为ANY模式

## 测试用例

```python
# 测试数据
test_data = {
    "n_content_spam": [
        {"class2": "广告信息", "probability": "0.9", "id": 1},
        {"class2": "其他", "probability": "0.8", "id": 2}
    ]
}

# 测试用例
assert filter_data("n_content_spam[ANY].class2 = '其他'", test_data) == True
assert filter_data("n_content_spam[ALL].class2 = '其他'", test_data) == False
assert filter_data("n_content_spam[ANY].class2 NOT IN ('广告信息',)", test_data) == True
assert filter_data("n_content_spam[ALL].class2 NOT IN ('广告信息',)", test_data) == False
```
