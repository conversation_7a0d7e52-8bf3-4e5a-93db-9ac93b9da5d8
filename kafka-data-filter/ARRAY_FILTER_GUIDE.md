# 数组过滤功能使用指南

## 概述

kafka-data-filter 现在支持对数组字段进行智能过滤，可以指定数组中的元素如何匹配过滤条件。

## 问题背景

在之前的版本中，当数据包含数组字段时，如：

```json
{
  "n_content_spam": [
    {
      "class2": "其他",
      "class1": "垃圾文",
      "probability": "0.80198514",
      "id": 10
    }
  ]
}
```

使用过滤规则 `n_content_spam.class2 NOT IN (...)` 会返回 `None`，导致过滤逻辑不正确。

## 新功能

### 数组访问语法

现在支持三种数组访问语法：

1. **显式ANY模式**: `field[ANY].subfield`
   - 数组中任意元素的子字段满足条件即可通过过滤

2. **显式ALL模式**: `field[ALL].subfield`
   - 数组中所有元素的子字段都必须满足条件才能通过过滤

3. **向后兼容模式**: `field.subfield`
   - 等同于 `field[ANY].subfield`，保持向后兼容

### 语义说明

#### ANY模式语义
- `field[ANY].sub = value` → 数组中至少有一个元素的sub字段等于value
- `field[ANY].sub IN (list)` → 数组中至少有一个元素的sub字段在list中
- `field[ANY].sub NOT IN (list)` → 数组中至少有一个元素的sub字段不在list中
- `field[ANY].sub > value` → 数组中至少有一个元素的sub字段大于value

#### ALL模式语义
- `field[ALL].sub = value` → 数组中所有元素的sub字段都等于value
- `field[ALL].sub IN (list)` → 数组中所有元素的sub字段都在list中
- `field[ALL].sub NOT IN (list)` → 数组中所有元素的sub字段都不在list中
- `field[ALL].sub > value` → 数组中所有元素的sub字段都大于value

## 使用示例

### 基本示例

```yaml
# 数组中任意元素的class2不在排除列表中
n_content_spam[ANY].class2 NOT IN ("广告信息", "股票资讯", "新闻合集")

# 数组中所有元素的class2都不在排除列表中
n_content_spam[ALL].class2 NOT IN ("广告信息", "股票资讯", "新闻合集")

# 向后兼容语法（等同于ANY模式）
n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集")
```

### 复杂规则示例

```yaml
# 完整的过滤规则
y_is_first_gather = 1 AND n_content_spam[ANY].class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐") AND y_yq_focus.label_name = "关注" AND n_media.nature IN ("境外媒体", "党媒", "政府部门")
```

### 数值比较示例

```yaml
# 数组中任意元素的概率大于0.8
n_content_spam[ANY].probability > 0.8

# 数组中所有元素的概率都大于0.5
n_content_spam[ALL].probability > 0.5
```

## 特殊情况处理

### 空数组
- **ANY模式**: 返回 `False`（没有元素满足条件）
- **ALL模式**: 返回 `True`（所有元素都满足条件，空集合的全称量化为真）

### 字段不存在
- 如果数组元素中某个字段不存在，该元素被视为不满足条件

### 类型不匹配
- 如果字段值类型与比较值类型不匹配，该元素被视为不满足条件

## 测试验证

可以使用以下命令测试数组过滤功能：

```bash
cd kafka-data-filter
python test_array_filter.py
```

## 配置文件更新

现有的 `filter_config.yaml` 已经更新为使用新的显式语法：

```yaml
filter:
  rules:
    - "y_is_first_gather = 1 AND n_content_spam[ANY].class2 NOT IN (\"广告信息\", \"股票资讯\", \"新闻合集\", \"历史科普\", \"明星娱乐\") AND y_yq_focus.label_name = \"关注\" AND n_media.nature IN (\"境外媒体\", \"党媒\",\"政府部门\")"
```

## 向后兼容性

- 现有的 `field.subfield` 语法仍然支持
- 默认行为等同于 `field[ANY].subfield`
- 不需要修改现有的过滤规则，但建议使用显式语法提高可读性

## 性能说明

- 数组过滤会遍历数组中的所有元素进行比较
- ANY模式支持短路求值（找到第一个满足条件的元素即返回）
- ALL模式需要检查所有元素
- 对于大型数组，建议优先使用ANY模式以获得更好的性能
