# Kafka数据过滤程序配置文件 - 输出优化示例
# 
# 此配置文件展示了新的输出功能优化特性
# 包括目录输出、时间戳命名、JSON数组格式等

# Kafka连接配置
kafka:
  # Kafka集群地址，多个地址用逗号分隔
  bootstrap_servers: "123.235.23.238:19301"

  # 要消费的topic
  topic: "moyeT_dfycb_website"

  # 消费者组ID
  group_id: "moye-check-data-optimized"

  # 消费起始位置：earliest（从最早消息开始）或 latest（从最新消息开始）
  auto_offset_reset: "latest"
  
  # 是否自动提交offset
  enable_auto_commit: true

  # 是否强制从最新消息开始消费（忽略已提交的offset）
  force_latest: false

# 过滤规则配置
filter:
  # 过滤规则列表，支持多个规则（OR逻辑：任一规则通过即可）
  rules:
    # 规则1：重要媒体的首次采集内容
    - "y_is_first_gather = 1 AND n_content_spam.class2 NOT IN (\"广告信息\", \"股票资讯\", \"新闻合集\", \"历史科普\", \"明星娱乐\") AND y_yq_focus.label_name = \"关注\" AND n_media.nature IN (\"境外媒体\", \"党媒\",\"政府部门\")"
    
    # 规则2：不确定关注度但来源可靠的内容
    - "y_is_first_gather = 1 AND n_content_spam.class2 NOT IN (\"广告信息\", \"股票资讯\", \"新闻合集\", \"历史科普\", \"明星娱乐\") AND y_yq_focus.label_name = \"不关注\" AND y_yq_focus.probability < 0.8 AND n_media.nature IN (\"境外媒体\", \"党媒\",\"政府部门\")"

# 输出配置 - 使用新的优化功能
output:
  # 输出类型：console（控制台）、file（文件）、both（同时输出到控制台和文件）
  type: "both"
  
  # 输出格式：json_array（JSON数组格式，新功能）
  format: "json_array"
  
  # 输出目录路径（新功能）
  output_directory: "./filtered_output"
  
  # 文件命名策略（新功能）
  file_naming:
    # 使用时间戳命名，确保每次启动生成新文件
    use_timestamp: true
    
    # 文件名前缀
    prefix: "filtered_messages"
    
    # 文件扩展名
    extension: ".json"
    
    # 时间戳格式
    timestamp_format: "%Y%m%d_%H%M%S"
  
  # JSON数组输出配置（新功能）
  json_array:
    # 启用增量追加模式
    incremental_append: true
    
    # 批量写入大小（优化性能）
    batch_size: 20
    
    # 每次启动创建新文件
    new_file_on_startup: true
  
  # 是否显示过滤详情
  show_filter_details: false

# 统计配置
statistics:
  # 是否启用统计功能
  enabled: true
  
  # 统计报告输出间隔（秒）
  interval: 30
  
  # 是否显示详细统计信息
  show_details: true

# 日志配置
logging:
  # 日志级别：DEBUG、INFO、WARNING、ERROR、CRITICAL
  level: "INFO"

# 自动清理配置
auto_cleanup:
  # 是否启用程序退出时自动删除消费者组
  enabled: true

  # 清理操作超时时间（秒）
  timeout: 30

  # 清理前是否备份消费者组信息
  backup_before_cleanup: true

  # 是否在异常退出时也执行清理
  cleanup_on_signal: true

# 输出优化配置说明：
# 
# 1. 目录输出支持：
#    - output_directory: 指定输出目录，会自动创建不存在的目录
#    - 支持相对路径和绝对路径
#
# 2. 时间戳文件命名：
#    - use_timestamp: true 启用时间戳命名
#    - 生成格式：filtered_messages_20250822_165645.json
#    - 确保文件名唯一性，避免覆盖
#
# 3. JSON数组格式：
#    - format: "json_array" 启用JSON数组输出
#    - 输出标准JSON数组格式：[{...}, {...}, {...}]
#    - 支持增量追加和批量写入优化
#
# 4. 性能优化：
#    - batch_size: 批量写入大小，减少I/O操作
#    - incremental_append: 支持追加到现有文件
#    - 原子性写入确保文件完整性
#
# 5. 向后兼容：
#    - 保持对原有配置格式的支持
#    - 可以混合使用新旧配置选项
