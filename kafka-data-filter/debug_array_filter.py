#!/usr/bin/env python3
"""
调试数组过滤问题的脚本
用于分析当前DataAccessor和FilterParser如何处理数组字段
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

# 直接导入模块
from kafka_data_filter.data_accessor import DataAccessor
from kafka_data_filter.filter_parser import FilterParser

def test_current_array_handling():
    """测试当前的数组处理逻辑"""
    print("=== 测试当前数组处理逻辑 ===")
    
    # 创建测试数据，模拟实际的JSON结构
    test_data = {
        "n_content_spam": [
            {
                "class2": "其他",
                "class1": "垃圾文", 
                "probability": "0.80198514",
                "id": 10
            }
        ],
        "y_yq_focus": {
            "label_name": "关注",
            "probability": 0.9
        },
        "n_media": {
            "nature": "党媒"
        },
        "y_is_first_gather": 1
    }
    
    # 测试DataAccessor
    accessor = DataAccessor()
    
    print("\n1. 测试DataAccessor对数组字段的访问:")
    
    # 测试直接访问数组
    result1 = accessor.get_field_value(test_data, "n_content_spam")
    print(f"n_content_spam = {result1}")
    print(f"类型: {type(result1)}")
    
    # 测试访问数组中的字段（当前可能有问题的地方）
    result2 = accessor.get_field_value(test_data, "n_content_spam.class2")
    print(f"n_content_spam.class2 = {result2}")
    print(f"类型: {type(result2)}")
    
    # 测试通过索引访问
    result3 = accessor.get_field_value(test_data, "n_content_spam.0.class2")
    print(f"n_content_spam.0.class2 = {result3}")
    print(f"类型: {type(result3)}")
    
    # 测试正常的嵌套对象访问
    result4 = accessor.get_field_value(test_data, "y_yq_focus.label_name")
    print(f"y_yq_focus.label_name = {result4}")
    
    print("\n2. 测试FilterParser对数组字段的过滤:")
    
    parser = FilterParser()
    
    # 测试当前配置文件中的规则
    rule = 'n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐")'
    
    try:
        print(f"测试规则: {rule}")
        result = parser.filter_data(rule, test_data)
        print(f"过滤结果: {result}")
        
        # 分析为什么会得到这个结果
        field_value = accessor.get_field_value(test_data, "n_content_spam.class2")
        print(f"字段值: {field_value}")
        
        if field_value is None:
            print("问题确认: n_content_spam.class2 返回 None，无法正确进行比较")
        
    except Exception as e:
        print(f"规则执行出错: {e}")

def test_expected_behavior():
    """测试期望的行为"""
    print("\n=== 期望的数组过滤行为 ===")
    
    test_data = {
        "n_content_spam": [
            {
                "class2": "广告信息",  # 应该被过滤掉
                "class1": "垃圾文",
                "probability": "0.9",
                "id": 1
            },
            {
                "class2": "其他",      # 应该通过过滤
                "class1": "垃圾文", 
                "probability": "0.8",
                "id": 2
            }
        ]
    }
    
    print("测试数据包含两个元素:")
    print("- 元素1: class2='广告信息' (应该被排除)")
    print("- 元素2: class2='其他' (应该通过)")
    
    print("\n期望的过滤行为:")
    print("1. ANY模式: n_content_spam[ANY].class2 NOT IN (...)")
    print("   - 如果数组中任意元素的class2不在排除列表中，则通过过滤")
    print("   - 结果应该是: True (因为'其他'不在排除列表中)")
    
    print("\n2. ALL模式: n_content_spam[ALL].class2 NOT IN (...)")
    print("   - 只有当数组中所有元素的class2都不在排除列表中，才通过过滤")
    print("   - 结果应该是: False (因为'广告信息'在排除列表中)")

def test_fixed_array_filtering():
    """测试修复后的数组过滤功能"""
    print("\n=== 测试修复后的数组过滤功能 ===")

    # 使用实际输出文件中的数据结构
    test_data = {
        "n_content_spam": [
            {
                "class2": "其他",
                "class1": "垃圾文",
                "probability": "0.80198514",
                "id": 10
            }
        ],
        "y_yq_focus": {
            "label_name": "关注",
            "probability": 0.9
        },
        "n_media": {
            "nature": "党媒"
        },
        "y_is_first_gather": 1
    }

    parser = FilterParser()

    print("1. 测试原始规则（修复前会返回错误结果）:")
    original_rule = 'n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐")'
    result1 = parser.filter_data(original_rule, test_data)
    print(f"原始规则结果: {result1} (现在应该正确返回True)")

    print("\n2. 测试新的显式ANY语法:")
    any_rule = 'n_content_spam[ANY].class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐")'
    result2 = parser.filter_data(any_rule, test_data)
    print(f"ANY模式结果: {result2} (应该返回True)")

    print("\n3. 测试完整的配置文件规则:")
    full_rule = 'y_is_first_gather = 1 AND n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐") AND y_yq_focus.label_name = "关注" AND n_media.nature IN ("境外媒体", "党媒","政府部门")'
    result3 = parser.filter_data(full_rule, test_data)
    print(f"完整规则结果: {result3} (应该返回True)")

    print("\n4. 测试问题数据（应该被过滤掉）:")
    problem_data = {
        "n_content_spam": [
            {
                "class2": "广告信息",  # 这个应该被过滤掉
                "class1": "垃圾文",
                "probability": "0.80198514",
                "id": 10
            }
        ],
        "y_yq_focus": {
            "label_name": "关注",
            "probability": 0.9
        },
        "n_media": {
            "nature": "党媒"
        },
        "y_is_first_gather": 1
    }

    result4 = parser.filter_data(full_rule, problem_data)
    print(f"问题数据结果: {result4} (应该返回False)")

if __name__ == "__main__":
    test_current_array_handling()
    test_expected_behavior()
    test_fixed_array_filtering()
